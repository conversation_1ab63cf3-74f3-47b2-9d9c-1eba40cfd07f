# 司机里程分析页面 - 后台接口数据格式规范

## 📋 接口概览

里程分析页面需要以下几个主要接口：

1. **统计数据接口** - 获取汇总统计信息
2. **图表数据接口** - 获取趋势图表数据  
3. **明细表格接口** - 获取分页的明细数据
4. **司机详情接口** - 获取单个司机的详细信息
5. **车辆详情接口** - 获取司机关联的车辆信息

## 🔍 1. 统计数据接口

### 接口地址
```
GET /api/analysis/driver-mileage/statistics
```

### 请求参数
```json
{
  "analysisType": "3",           // 分析类型：0-年度，2-月度，3-日，4-小时
  "startTime": "2024-01-01",     // 开始时间
  "endTime": "2024-01-31",       // 结束时间
  "deptId": 123,                 // 组织机构ID（可选）
  "driverId": 456,               // 司机ID（可选）
  "driverName": "张三",          // 司机姓名（可选）
  "workNumber": "D001",          // 工号（可选）
  "routeId": "115"               // 线路ID（可选）
}
```

### 响应数据
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalMileage": "89652.5",        // 总里程(公里)
    "operationalMileage": "62756.3",  // 运营里程(公里)
    "nonOperationalMileage": "26896.2", // 非运营里程(公里)
    "driverCount": 368                // 驾驶员数量
  }
}
```

## 📈 2. 图表数据接口

### 接口地址
```
GET /api/analysis/driver-mileage/chart-data
```

### 请求参数
```json
{
  "analysisType": "3",           // 分析类型
  "startTime": "2024-01-01",     // 开始时间
  "endTime": "2024-01-31",       // 结束时间
  "deptId": 123,                 // 组织机构ID（可选）
  "driverId": 456,               // 司机ID（可选）
  "routeId": "115"               // 线路ID（可选）
}
```

### 响应数据
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "xAxisData": [                    // X轴数据（根据analysisType变化）
      "2024-01-01", "2024-01-02", "2024-01-03", "..."
    ],
    "series": [
      {
        "name": "运营里程",
        "type": "line",
        "data": [120.5, 135.2, 98.7, 156.3, 142.8]  // 对应X轴的运营里程数据
      },
      {
        "name": "非运营里程", 
        "type": "line",
        "data": [45.2, 38.9, 52.1, 41.6, 48.3]      // 对应X轴的非运营里程数据
      }
    ]
  }
}
```

## 📊 3. 明细表格接口

### 接口地址
```
GET /api/analysis/driver-mileage/detail-list
```

### 请求参数
```json
{
  "pageNum": 1,                  // 页码
  "pageSize": 10,                // 每页大小
  "analysisType": "3",           // 分析类型
  "startTime": "2024-01-01",     // 开始时间
  "endTime": "2024-01-31",       // 结束时间
  "deptId": 123,                 // 组织机构ID（可选）
  "driverName": "张三",          // 司机姓名（可选）
  "workNumber": "D001",          // 工号（可选）
  "routeId": "115"               // 线路ID（可选）
}
```

### 响应数据
```json
{
  "code": 200,
  "msg": "操作成功", 
  "data": {
    "total": 156,
    "rows": [
      {
        "driverId": 1001,
        "driverName": "张志明",
        "workNumber": "D001",
        "deptName": "第一车队",
        "routeName": "115路",
        "averageSpeed": 35.6,          // 平均速度(km/h)
        "runningTime": 8.5,            // 运行时长(根据analysisType单位变化)
        "vehicleCount": 2,             // 车辆数
        "phoneNumber": "13800138001",
        "currentVehicle": "京A12345",
        "shift": "早班",
        
        // 动态日期字段（根据时间范围生成）
        "operational_2024_01_01": 125.6,    // 2024-01-01运营里程
        "nonOperational_2024_01_01": 45.2,  // 2024-01-01非运营里程
        "operational_2024_01_02": 132.8,    // 2024-01-02运营里程
        "nonOperational_2024_01_02": 38.9,  // 2024-01-02非运营里程
        // ... 更多日期字段
        
        // 汇总字段
        "totalOperationalMileage": 3856.7,  // 总运营里程
        "totalNonOperationalMileage": 1245.3, // 总非运营里程
        "totalMileage": 5102.0             // 总里程
      }
      // ... 更多记录
    ]
  }
}
```

## 👤 4. 司机详情接口

### 接口地址
```
GET /api/analysis/driver-mileage/driver-detail/{driverId}
```

### 请求参数
```json
{
  "statisticDate": "2024-01-15"  // 统计日期
}
```

### 响应数据
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "driverId": 1001,
    "driverName": "张志明",
    "workNumber": "D001",
    "phoneNumber": "13800138001",
    "currentVehicle": "京A12345",
    "routeName": "115路",
    "deptName": "第一车队",
    "shift": "早班",
    "statisticDate": "2024-01-15",
    "dailyMileage": 156.8,         // 当日驾驶里程(km)
    "monthlyMileage": 3245.6,      // 月度预估里程(km)
    "totalMileage": 45678.9,       // 历史累计里程(km)
    "averageSpeed": 35.6,          // 平均速度(km/h)
    "drivingTime": 8.5,            // 驾驶时长(小时)
    "workloadEfficiency": 18.4,    // 工作效率(km/h)
    "violationCount": 2,           // 违规次数
    "drivingScore": 85,            // 驾驶评分
    "maxSpeed": 65.2,              // 最大时速(km/h)
    "drivingExperience": 8,        // 驾龄(年)
    "fuelConsumption": 45.6,       // 油耗(L)
    "mileageStatus": "normal",     // 里程状态：normal-正常，high-偏高，low-偏低
    "routeDetail": "火车站-市中心-开发区", // 行驶路线
    "violationDetail": "超速2次",   // 违规详情
    "remark": "表现良好"           // 工作备注
  }
}
```

## 🚗 5. 车辆详情接口

### 接口地址
```
GET /api/analysis/driver-mileage/vehicle-detail/{driverId}
```

### 响应数据
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "driverId": 1001,
    "driverName": "张志明",
    "vehicles": [
      {
        "vehicleId": 2001,
        "plateNumber": "京A12345",
        "vehicleNumber": "V001",
        "routeName": "115路",
        "status": "运营中",           // 状态：运营中、维修中、停运
        "mileage": 1256.8,          // 该车辆里程
        "lastDriveTime": "2024-01-15 18:30:00"
      },
      {
        "vehicleId": 2002,
        "plateNumber": "京A67890", 
        "vehicleNumber": "V002",
        "routeName": "115路",
        "status": "维修中",
        "mileage": 856.2,
        "lastDriveTime": "2024-01-14 16:45:00"
      }
    ]
  }
}
```

## 🔧 6. 辅助接口

### 6.1 线路选项接口
```
GET /api/basic/routes/options
```

响应：
```json
{
  "code": 200,
  "data": [
    {"label": "115路", "value": "115"},
    {"label": "135路", "value": "135"},
    {"label": "201路", "value": "201"}
  ]
}
```

### 6.2 组织机构树接口
```
GET /api/system/dept/tree
```

响应：
```json
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "label": "运输公司",
      "children": [
        {"id": 2, "label": "第一车队"},
        {"id": 3, "label": "第二车队"}
      ]
    }
  ]
}
```

## 📝 重要说明

### 1. 动态日期字段生成规则
- 字段名格式：`operational_YYYY_MM_DD` 和 `nonOperational_YYYY_MM_DD`
- 根据 `analysisType` 和时间范围动态生成
- 年度分析：`operational_2024`, `nonOperational_2024`
- 月度分析：`operational_2024_01`, `nonOperational_2024_01`
- 日分析：`operational_2024_01_01`, `nonOperational_2024_01_01`
- 小时分析：`operational_2024_01_01_08`, `nonOperational_2024_01_01_08`

### 2. 分析类型说明
- `0`: 年度分析
- `2`: 月度分析  
- `3`: 日分析
- `4`: 小时分析

### 3. 数据精度要求
- 里程数据保留1位小数
- 速度数据保留1位小数
- 时间数据根据类型调整精度

### 4. 性能考虑
- 建议对大数据量查询进行分页处理
- 图表数据建议限制在合理范围内（如最多31个数据点）
- 可考虑添加数据缓存机制
