<template>
  <div class='maintenance-management-fullscreen'>
    <el-row :gutter='20'>
      <!-- 组织机构筛选树 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <Tools />
              </el-icon>
              <span>组织机构筛选</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入组织机构名称' prefix-icon='Search' clearable />
            </div>
            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            />
          </div>
        </div>
      </el-col>

      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 顶部工具栏 -->
          <header class='content-header'>
            <div class='header-left'>
              <div class='title-section'>
                <el-icon class='title-icon'>
                  <Tools />
                </el-icon>
                <h1 class='page-title'>维修保养管理</h1>
              </div>
              <div class='stats-cards'>
                <div class='stat-card'>
                  <div class='stat-value'>{{ maintenanceList.length }}</div>
                  <div class='stat-label'>总记录</div>
                </div>
                <div class='stat-card online'>
                  <div class='stat-value'>{{ maintenanceList.filter(m => m.status === 'completed').length }}</div>
                  <div class='stat-label'>已完成</div>
                </div>
                <div class='stat-card warning'>
                  <div class='stat-value'>{{ maintenanceList.filter(m => m.status === 'in_progress').length }}</div>
                  <div class='stat-label'>进行中</div>
                </div>
                <div class='stat-card danger'>
                  <div class='stat-value'>{{ maintenanceList.filter(m => m.status === 'pending').length }}</div>
                  <div class='stat-label'>待处理</div>
                </div>
              </div>
            </div>
          </header>

          <!-- 搜索表单 -->
          <div class='search-section' v-show='showSearch'>
            <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">
              <el-form-item label="车牌号" prop="plateNumber">
                <el-input
                  v-model="queryParams.plateNumber"
                  placeholder="请输入车牌号"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="维修日期" prop="maintenanceDate">
                <el-date-picker
                  v-model="queryParams.maintenanceDate"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
              <el-form-item label="类型" prop="maintenanceType">
                <el-select v-model="queryParams.maintenanceType" placeholder="请选择类型" clearable>
                  <el-option label="保养" value="maintenance" />
                  <el-option label="维修" value="repair" />
                  <el-option label="年检" value="inspection" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 操作按钮区域 -->
          <div class='action-section'>
            <div class='action-buttons'>
              <el-button type="info" plain icon="Back" @click="handleBack">返回</el-button>
              <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
              <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
              <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
              <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </div>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
          </div>

          <!-- 维修保养记录表格 -->
          <div class='table-container' v-loading="loading">
            <el-table :data="maintenanceList" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="车牌号" align="center" prop="plateNumber" />
              <el-table-column label="车辆编号" align="center" prop="vehicleNumber" />
              <el-table-column label="维修日期" align="center" prop="maintenanceDate" width="100">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.maintenanceDate, '{y}-{m}-{d}') }}</span>
                </template>
              </el-table-column>
              <el-table-column label="类型" align="center" prop="maintenanceType">
                <template #default="scope">
                  <el-tag v-if="scope.row.maintenanceType === 'maintenance'" type="success">保养</el-tag>
                  <el-tag v-else-if="scope.row.maintenanceType === 'repair'" type="warning">维修</el-tag>
                  <el-tag v-else type="info">年检</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="维修项目" align="center" prop="maintenanceItem" show-overflow-tooltip />
              <el-table-column label="费用(元)" align="center" prop="cost" />
              <el-table-column label="维修厂" align="center" prop="repairShop" show-overflow-tooltip />
              <el-table-column label="里程数(km)" align="center" prop="mileage" />
              <el-table-column label="状态" align="center" prop="status">
                <template #default="scope">
                  <el-tag v-if="scope.row.status === 'completed'" type="success">已完成</el-tag>
                  <el-tag v-else-if="scope.row.status === 'in_progress'" type="warning">进行中</el-tag>
                  <el-tag v-else type="info">待处理</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width" fixed="right">
                <template #default="scope">
                  <div class="operation-buttons">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button link type="info" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                    <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <pagination
            v-show="total>0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>

        <!-- 添加或修改维修保养记录对话框 -->
        <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="maintenanceRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="车辆" prop="vehicleId">
              <el-select v-model="form.vehicleId" placeholder="请选择车辆" @change="handleVehicleChange">
                <el-option
                  v-for="vehicle in vehicleOptions"
                  :key="vehicle.vehicleId"
                  :label="`${vehicle.plateNumber}(${vehicle.vehicleNumber})`"
                  :value="vehicle.vehicleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维修日期" prop="maintenanceDate">
              <el-date-picker
                v-model="form.maintenanceDate"
                type="datetime"
                placeholder="选择维修日期"
                value-format="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="类型" prop="maintenanceType">
              <el-select v-model="form.maintenanceType" placeholder="请选择类型">
                <el-option label="保养" value="maintenance" />
                <el-option label="维修" value="repair" />
                <el-option label="年检" value="inspection" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="待处理" value="pending" />
                <el-option label="进行中" value="in_progress" />
                <el-option label="已完成" value="completed" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="费用(元)" prop="cost">
              <el-input-number v-model="form.cost" :precision="2" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当前里程(km)" prop="mileage">
              <el-input-number v-model="form.mileage" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="维修项目" prop="maintenanceItem">
          <el-input v-model="form.maintenanceItem" placeholder="请输入维修项目" />
        </el-form-item>
        <el-form-item label="维修厂" prop="repairShop">
          <el-input v-model="form.repairShop" placeholder="请输入维修厂名称" />
        </el-form-item>
        <el-form-item label="维修内容" prop="maintenanceContent">
          <el-input v-model="form.maintenanceContent" type="textarea" :rows="3" placeholder="请输入详细维修内容" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog title="维修保养详情" v-model="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="车牌号">{{ detailData.plateNumber }}</el-descriptions-item>
        <el-descriptions-item label="车辆编号">{{ detailData.vehicleNumber }}</el-descriptions-item>
        <el-descriptions-item label="维修日期">{{ detailData.maintenanceDate }}</el-descriptions-item>
        <el-descriptions-item label="类型">
          <el-tag v-if="detailData.maintenanceType === 'maintenance'" type="success">保养</el-tag>
          <el-tag v-else-if="detailData.maintenanceType === 'repair'" type="warning">维修</el-tag>
          <el-tag v-else type="info">年检</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="维修项目" :span="2">{{ detailData.maintenanceItem }}</el-descriptions-item>
        <el-descriptions-item label="费用">{{ detailData.cost }} 元</el-descriptions-item>
        <el-descriptions-item label="里程数">{{ detailData.mileage }} km</el-descriptions-item>
        <el-descriptions-item label="维修厂" :span="2">{{ detailData.repairShop }}</el-descriptions-item>
        <el-descriptions-item label="状态" :span="2">
          <el-tag v-if="detailData.status === 'completed'" type="success">已完成</el-tag>
          <el-tag v-else-if="detailData.status === 'in_progress'" type="warning">进行中</el-tag>
          <el-tag v-else type="info">待处理</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="维修内容" :span="2">{{ detailData.maintenanceContent }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailData.remark }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
        </div>
      </template>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="VehicleMaintenance">
import { Tools } from '@element-plus/icons-vue';
import { listMaintainLog, getMaintainLog, delMaintainLog, addMaintainLog, updateMaintainLog } from '@/api/basic/maintainLog';
import { listVehicle } from '@/api/basic/vehicle';
import { deptTreeSelect } from '@/api/system/user';
import { parseTime } from '@/utils/ruoyi';

const { proxy } = getCurrentInstance();

const maintenanceRef = ref();
const queryRef = ref();
const maintenanceList = ref([]);
const vehicleOptions = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const detailData = ref({});

// 组织机构树相关数据
const deptName = ref('');
const deptOptions = ref([]);
const deptTreeRef = ref();

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    plateNumber: null,
    maintenanceDate: null,
    maintenanceType: null,
    deptId: null,
    vehicleId: null
  },
  rules: {
    vehicleId: [
      { required: true, message: "请选择车辆", trigger: "change" }
    ],
    maintenanceDate: [
      { required: true, message: "请选择维修日期", trigger: "change" }
    ],
    maintenanceType: [
      { required: true, message: "请选择类型", trigger: "change" }
    ],
    maintenanceItem: [
      { required: true, message: "请输入维修项目", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 组织机构树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** 查询组织机构下拉树结构 */
async function getTreeSelect() {
  try {
    const res = await deptTreeSelect();
    deptOptions.value = res.data;
  } catch (error) {
    console.error('获取组织机构树失败:', error);
    deptOptions.value = [];
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  // 点击组织机构节点，按组织机构筛选
  queryParams.value.deptId = data.id;
  queryParams.value.vehicleId = null;
  handleQuery();
}

/** 查询维修保养记录列表 */
function getList() {
  loading.value = true;
  listMaintainLog(queryParams.value).then(response => {
    const rows = response.rows || [];
    maintenanceList.value = rows.map(item => ({
      ...item,
      maintenanceId: item.id || item.maintenanceId
    }));
    total.value = response.total || 0;
    loading.value = false;
  }).catch(error => {
    console.error('获取维修保养记录失败:', error);
    loading.value = false;
    maintenanceList.value = [];
    total.value = 0;
    proxy.$modal.msgError('获取维修保养记录失败，请稍后重试');
  });
}

/** 获取车辆选项 */
function getVehicleOptions() {
  listVehicle({ pageSize: 1000 }).then(response => {
    const rows = response.rows || [];
    vehicleOptions.value = rows.map(vehicle => ({
      vehicleId: vehicle.id || vehicle.vehicleId,
      plateNumber: vehicle.plateNumber,
      vehicleNumber: vehicle.vehicleNumber
    }));
  }).catch(error => {
    console.error('获取车辆列表失败:', error);
    vehicleOptions.value = [];
  });
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  queryRef.value?.resetFields();
  queryRef.value?.clearValidate();
  queryParams.value.deptId = null;
  queryParams.value.vehicleId = null;
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.maintenanceId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  getVehicleOptions();
  open.value = true;
  title.value = "添加维修保养记录";
}

function handleUpdate(row) {
  reset();
  getVehicleOptions();
  const maintenanceId = row.maintenanceId || row.id;
  getMaintainLog(maintenanceId).then(response => {
    form.value = {
      ...response.data,
      maintenanceId: response.data.id || response.data.maintenanceId
    };
    open.value = true;
    title.value = "修改维修保养记录";
  }).catch(error => {
    console.error('获取记录详情失败:', error);
    proxy.$modal.msgError('获取记录详情失败');
  });
}

function handleDetail(row) {
  const maintenanceId = row.maintenanceId || row.id;
  getMaintainLog(maintenanceId).then(response => {
    detailData.value = {
      ...response.data,
      maintenanceId: response.data.id || response.data.maintenanceId
    };
    detailOpen.value = true;
  }).catch(error => {
    console.error('获取记录详情失败:', error);
    proxy.$modal.msgError('获取记录详情失败');
  });
}

function handleDelete(row) {
  const maintenanceIds = row.maintenanceId || ids.value;
  proxy.$modal.confirm('是否确认删除维修保养记录编号为"' + maintenanceIds + '"的数据项？').then(function() {
    return delMaintainLog(maintenanceIds);
  }).then(() => {
    proxy.$modal.msgSuccess("删除成功");
    getList();
  }).catch(() => {});
}

function handleBack() {
  proxy.$router.push('/basic/vehicle');
}

function handleExport() {
  proxy.$modal.confirm('是否确认导出所有维修保养记录数据项？').then(() => {
    proxy.$modal.msgSuccess("导出成功");
  });
}

function handleVehicleChange(vehicleId) {
  const vehicle = vehicleOptions.value.find(v => v.vehicleId === vehicleId);
  if (vehicle) {
    form.value.plateNumber = vehicle.plateNumber;
    form.value.vehicleNumber = vehicle.vehicleNumber;
  }
}

function submitForm() {
  proxy.$refs["maintenanceRef"].validate(valid => {
    if (valid) {
      if (form.value.maintenanceId != null) {
        // 修改
        const updateData = { ...form.value, id: form.value.maintenanceId };
        updateMaintainLog(updateData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        }).catch(error => {
          console.error('修改失败:', error);
          proxy.$modal.msgError('修改失败，请稍后重试');
        });
      } else {
        // 新增
        addMaintainLog(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        }).catch(error => {
          console.error('新增失败:', error);
          proxy.$modal.msgError('新增失败，请稍后重试');
        });
      }
    }
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    maintenanceId: null,
    vehicleId: null,
    plateNumber: null,
    vehicleNumber: null,
    maintenanceDate: null,
    maintenanceType: null,
    maintenanceItem: null,
    maintenanceContent: null,
    cost: null,
    repairShop: null,
    mileage: null,
    status: "pending",
    remark: null
  };
  maintenanceRef.value?.resetFields();
  maintenanceRef.value?.clearValidate();
}

getList();
getTreeSelect();
</script>

<style scoped>
/* 全屏布局基础样式 - 科技感深色主题 */
.maintenance-management-fullscreen {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 左侧面板样式 */
.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.search-box {
  margin-bottom: 16px;
}

.dept-tree {
  background: transparent;
  color: #cbd5e1;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dept-icon {
  color: #60a5fa;
  font-size: 16px;
}

.vehicle-icon {
  color: #34d399;
  font-size: 14px;
}

.node-label {
  font-size: 14px;
}

/* 右侧内容区域 */
.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 顶部工具栏 */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 24px;
  color: #60a5fa;
}

.page-title {
  font-size: 20px;
  font-weight: 700;
  color: #f8fafc;
  margin: 0;
}

.stats-cards {
  display: flex;
  gap: 12px;
}

.stat-card {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  text-align: center;
  min-width: 80px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
}

.stat-card.online {
  border-color: rgba(34, 197, 94, 0.4);
  background: rgba(34, 197, 94, 0.1);
}

.stat-card.warning {
  border-color: rgba(245, 158, 11, 0.4);
  background: rgba(245, 158, 11, 0.1);
}

.stat-card.danger {
  border-color: rgba(239, 68, 68, 0.4);
  background: rgba(239, 68, 68, 0.1);
}

.stat-value {
  font-size: 22px;
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 4px;
}

/* 搜索区域 */
.search-section {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 表格容器 */
.table-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

/* 操作按钮布局 */
.operation-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  flex-wrap: nowrap;
  padding: 4px 0;
  width: 100%;
}

.operation-buttons .el-button {
  margin: 0 !important;
  min-width: auto;
  padding: 6px 10px;
  font-size: 12px;
  border-radius: 4px;
}

/* Element UI组件深色主题覆盖 */
:deep(.el-form-item__label) {
  color: #e5e7eb !important;
}

:deep(.el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e5e7eb !important;
}

:deep(.el-input__inner) {
  color: #e5e7eb !important;
}

:deep(.el-input__inner::placeholder) {
  color: #64748b !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
}

:deep(.el-date-editor .el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
}

/* 表格深色主题样式 */
:deep(.el-table) {
  background: transparent !important;
  color: #e5e7eb !important;
}

:deep(.el-table tr) {
  background: rgba(15, 23, 42, 0.6) !important;
}

:deep(.el-table tr:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

:deep(.el-table th.el-table__cell) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
  font-weight: 600 !important;
}

:deep(.el-table td.el-table__cell) {
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e5e7eb !important;
}

:deep(.el-table__empty-block) {
  background: rgba(15, 23, 42, 0.6) !important;
}

:deep(.el-table__empty-text) {
  color: #94a3b8 !important;
}

/* 选择框样式 */
:deep(.el-checkbox) {
  .el-checkbox__input {
    .el-checkbox__inner {
      background: rgba(10, 22, 48, 0.8) !important;
      border-color: rgba(147, 197, 253, 0.3) !important;
    }
  }

  &.is-checked .el-checkbox__input .el-checkbox__inner {
    background: #3b82f6 !important;
    border-color: #3b82f6 !important;
  }
}

/* 分页组件样式 */
:deep(.el-pagination) {
  .el-pagination__total,
  .el-pagination__jump,
  .el-pager li,
  .el-pagination__sizes .el-input__inner {
    color: #e5e7eb !important;
  }

  .el-pager li {
    background: rgba(10, 22, 48, 0.6) !important;
    border: 1px solid rgba(147, 197, 253, 0.2) !important;

    &:hover {
      background: rgba(59, 130, 246, 0.2) !important;
    }

    &.is-active {
      background: #3b82f6 !important;
      color: #ffffff !important;
    }
  }

  .btn-prev,
  .btn-next {
    background: rgba(10, 22, 48, 0.6) !important;
    color: #e5e7eb !important;
    border: 1px solid rgba(147, 197, 253, 0.2) !important;

    &:hover {
      background: rgba(59, 130, 246, 0.2) !important;
    }
  }
}

/* 加载组件样式 */
:deep(.el-loading-mask) {
  background-color: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-loading-spinner) {
  .path {
    stroke: #3b82f6 !important;
  }

  .el-loading-text {
    color: #e5e7eb !important;
  }
}

/* 对话框深色主题 */
:deep(.el-dialog) {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%) !important;
  border: 1px solid rgba(147, 197, 253, 0.2) !important;

  .el-dialog__header {
    border-bottom: 1px solid rgba(147, 197, 253, 0.2) !important;
  }

  .el-dialog__title {
    color: #f8fafc !important;
  }

  .el-dialog__body {
    color: #e5e7eb !important;
  }
}

/* 数字输入框样式 */
:deep(.el-input-number) {
  .el-input__wrapper {
    background: rgba(10, 22, 48, 0.8) !important;
    border-color: rgba(147, 197, 253, 0.3) !important;
  }

  .el-input-number__decrease,
  .el-input-number__increase {
    background: rgba(10, 22, 48, 0.8) !important;
    border-color: rgba(147, 197, 253, 0.3) !important;
    color: #e5e7eb !important;

    &:hover {
      background: rgba(59, 130, 246, 0.2) !important;
      color: #ffffff !important;
    }
  }
}

/* 文本域样式 */
:deep(.el-textarea) {
  .el-textarea__inner {
    background: rgba(10, 22, 48, 0.8) !important;
    border-color: rgba(147, 197, 253, 0.3) !important;
    color: #e5e7eb !important;

    &::placeholder {
      color: #64748b !important;
    }
  }
}

/* 描述列表样式 */
:deep(.el-descriptions) {
  .el-descriptions__header {
    .el-descriptions__title {
      color: #f8fafc !important;
    }
  }

  .el-descriptions__body {
    .el-descriptions__table {
      .el-descriptions__cell {
        border-color: rgba(147, 197, 253, 0.2) !important;

        &.is-bordered-label {
          background: rgba(30, 41, 59, 0.8) !important;
          color: #f8fafc !important;
        }

        &.is-bordered-content {
          background: rgba(15, 23, 42, 0.6) !important;
          color: #e5e7eb !important;
        }
      }
    }
  }
}

/* 树形组件深色主题 */
:deep(.el-tree-node) {
  background: transparent;
}

:deep(.el-tree-node:hover > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 6px;
}

:deep(.el-tree-node__content) {
  background: transparent !important;
  border: none !important;
  color: #e5e7eb !important;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

:deep(.el-tree-node__content:hover) {
  background-color: rgba(59, 130, 246, 0.15) !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
}

:deep(.el-tree-node__expand-icon) {
  color: #64748b !important;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree .el-tree-node__children) {
  overflow: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .maintenance-management-fullscreen {
    padding: 10px;
  }

  .header-left {
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }

  .stats-cards {
    width: 100%;
    justify-content: space-around;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
}
</style>
