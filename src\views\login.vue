<template>
  <div class="login">
    <!-- 全屏背景 -->
    <div class="login-bg"></div>

    <!-- 科技感装饰元素 -->
    <div class="tech-overlay">
      <div class="tech-grid"></div>
      <div class="floating-particles">
        <div class="particle" v-for="i in 20" :key="i" :style="getParticleStyle(i)"></div>
      </div>
    </div>

    <!-- 登录表单容器 -->
    <div class="login-form-container">
      <div class="glass-panel">
        <div class="tech-decoration"></div>
        <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
          <div class="form-header">
            <div class="logo-section">
              <div class="logo-icon">
                <svg viewBox="0 0 24 24" width="32" height="32" fill="currentColor">
                  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
                </svg>
              </div>
              <h1 class="system-name">智慧公交</h1>
            </div>
            <h2 class="form-title">欢迎登录</h2>
            <p class="form-subtitle">智慧公交调度管理平台</p>
          </div>

          <el-form-item prop="username">
            <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" placeholder="用户名" class="login-input">
              <template #prefix>
                <svg-icon icon-class="user" class="input-icon" />
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              size="large"
              auto-complete="off"
              placeholder="密码"
              class="login-input"
              @keyup.enter="handleLogin"
            >
              <template #prefix>
                <svg-icon icon-class="password" class="input-icon" />
              </template>
            </el-input>
          </el-form-item>

          <el-form-item v-if="captchaEnabled" prop="code" class="captcha-form-item">
            <div class="captcha-input-group">
              <el-input
                v-model="loginForm.code"
                size="large"
                auto-complete="off"
                placeholder="验证码"
                class="login-input captcha-input"
                @keyup.enter="handleLogin"
              >
                <template #prefix>
                  <svg-icon icon-class="validCode" class="input-icon" />
                </template>
              </el-input>
              <div class="captcha-code">
                <img :src="codeUrl" class="captcha-img" @click="getCode" title="点击刷新验证码" />
              </div>
            </div>
          </el-form-item>

          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button :loading="loading" size="large" type="primary" class="login-btn" @click.prevent="handleLogin">
              <span v-if="!loading">立即登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 底部版权信息 -->
    <div class="login-footer">
      <span>© 2025 TransitSync. All rights reserved.</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getCodeImg } from '@/api/login';
import { useUserStore } from '@/store/modules/user';
import { LoginData } from '@/api/types';
import { to } from 'await-to-js';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const userStore = useUserStore();
const router = useRouter();

const loginForm = ref<LoginData>({
  username: '',
  password: '',
  rememberMe: false,
  code: '',
  uuid: ''
} as LoginData);

const loginRules: ElFormRules = {
  username: [{ required: true, trigger: 'blur', message: '用户名不能为空' }],
  password: [{ required: true, trigger: 'blur', message: '密码不能为空' }],
  code: [{ required: true, trigger: 'change', message: '验证码不能为空' }]
};

const codeUrl = ref('');
const loading = ref(false);
const captchaEnabled = ref(true);
const redirect = ref('/');
const loginRef = ref<ElFormInstance>();

watch(
  () => router.currentRoute.value,
  (newRoute: any) => {
    redirect.value = newRoute.query && newRoute.query.redirect && decodeURIComponent(newRoute.query.redirect);
  },
  { immediate: true }
);

const handleLogin = () => {
  loginRef.value?.validate(async (valid: boolean, fields: any) => {
    if (valid) {
      loading.value = true;

      if (loginForm.value.rememberMe) {
        localStorage.setItem('username', String(loginForm.value.username));
        localStorage.setItem('password', String(loginForm.value.password));
        localStorage.setItem('rememberMe', String(loginForm.value.rememberMe));
      } else {
        localStorage.removeItem('username');
        localStorage.removeItem('password');
        localStorage.removeItem('rememberMe');
      }

      const [err] = await to(userStore.login(loginForm.value));
      if (!err) {
        const redirectUrl = redirect.value || '/';
        await router.push(redirectUrl);
        loading.value = false;
      } else {
        loading.value = false;
        if (captchaEnabled.value) {
          await getCode();
        }
      }
    } else {
      console.log('表单验证失败:', fields);
    }
  });
};

const getCode = async () => {
  const res = await getCodeImg();
  const { data } = res;
  captchaEnabled.value = data.captchaEnabled === undefined ? true : data.captchaEnabled;
  if (captchaEnabled.value) {
    codeUrl.value = 'data:image/gif;base64,' + data.img;
    loginForm.value.uuid = data.uuid;
  }
};

const getLoginData = () => {
  const username = localStorage.getItem('username');
  const password = localStorage.getItem('password');
  const rememberMe = localStorage.getItem('rememberMe');
  loginForm.value = {
    username: username === null ? String(loginForm.value.username) : username,
    password: password === null ? String(loginForm.value.password) : String(password),
    rememberMe: rememberMe === null ? false : Boolean(rememberMe)
  } as LoginData;
};

const getParticleStyle = (index: number) => {
  const delay = Math.random() * 5;
  const duration = 3 + Math.random() * 4;
  const size = 2 + Math.random() * 3;
  const left = Math.random() * 100;
  const opacity = 0.1 + Math.random() * 0.3;

  return {
    left: `${left}%`,
    animationDelay: `${delay}s`,
    animationDuration: `${duration}s`,
    width: `${size}px`,
    height: `${size}px`,
    opacity: opacity
  };
};

onMounted(() => {
  getCode();
  getLoginData();
});
</script>

<style lang="scss" scoped>
.login {
  position: relative;
  height: 100vh;
  overflow: hidden;
}

.login-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('@/assets/images/lobg.png') center center / cover no-repeat;
  z-index: 0;
}

.tech-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.03) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.particle {
  position: absolute;
  background: #3b82f6;
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
    transform: translateY(90vh) scale(1);
  }
  90% {
    opacity: 1;
    transform: translateY(-10vh) scale(1);
  }
  100% {
    transform: translateY(-20vh) scale(0);
    opacity: 0;
  }
}

.login-form-container {
  position: fixed;
  top: 50%;
  right: 60px;
  transform: translateY(-50%);
  width: 420px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.glass-panel {
  width: 100%;
  max-width: 380px;
  background: rgba(15, 23, 42, 0.85);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.8), transparent);
  }

  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.03) 0%, transparent 70%);
    animation: pulse 4s ease-in-out infinite;
  }
}

/* 添加左侧装饰线 */
.tech-decoration {
  position: absolute;
  left: -1px;
  top: 20%;
  bottom: 20%;
  width: 2px;
  background: linear-gradient(180deg, transparent, #3b82f6, transparent);
  opacity: 0.6;
  animation: glow 2s ease-in-out infinite alternate;
}

.login-form {
  width: 100%;
  position: relative;
  z-index: 1;

  .form-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    z-index: 2;

    .logo-section {
      position: relative;
      height: 32px;
      margin-bottom: 24px;
      display: flex;
      align-items: center;
      justify-content: center;

      .logo-icon {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) translateX(-60px);
        width: 32px;
        height: 32px;
        color: #3b82f6;
        display: flex;
        align-items: center;
        justify-content: center;
        filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.5));
        animation: glow 2s ease-in-out infinite alternate;
        
        svg {
          width: 100%;
          height: 100%;
          display: block;
        }
      }

      .system-name {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) translateX(12px);
        font-size: 26px;
        font-weight: 700;
        background: linear-gradient(135deg, #3b82f6, #06b6d4);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0;
        padding: 0;
        letter-spacing: 0.5px;
        text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        line-height: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        white-space: nowrap;
      }
    }

    .form-title {
      font-size: 24px;
      color: #f1f5f9;
      margin: 0 0 8px 0;
      font-weight: 500;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .form-subtitle {
      color: #94a3b8;
      font-size: 13px;
      margin: 0;
      font-weight: 400;
      opacity: 0.9;
    }
  }

  .login-input {
    margin-bottom: 20px;
    position: relative;
    z-index: 2;

    :deep(.el-input__wrapper) {
      height: 50px;
      border-radius: 12px;
      background: rgba(30, 41, 59, 0.8);
      border: 1px solid rgba(59, 130, 246, 0.2);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(59, 130, 246, 0.5);
        background: rgba(30, 41, 59, 0.9);
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
      }

      &.is-focus {
        border-color: #3b82f6;
        background: rgba(30, 41, 59, 0.95);
        box-shadow:
          0 0 0 2px rgba(59, 130, 246, 0.2),
          0 0 20px rgba(59, 130, 246, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
      }
    }

    :deep(.el-input__inner) {
      height: 48px;
      line-height: 48px;
      font-size: 15px;
      color: #f1f5f9;
      background: transparent;

      &::placeholder {
        color: #64748b;
      }
    }

    .input-icon {
      color: #3b82f6;
      font-size: 18px;
      filter: drop-shadow(0 0 4px rgba(59, 130, 246, 0.3));
    }
  }

  // 验证码表单项
  .captcha-form-item {
    margin-bottom: 16px;

    .captcha-input-group {
      display: flex;
      align-items: center;
      gap: 12px;

      .captcha-input {
        flex: 1;
        margin-bottom: 0;
      }
    }
  }

  .captcha-code {
    width: 120px;
    height: 50px;
    flex-shrink: 0;

    .captcha-img {
      width: 100%;
      height: 100%;
      cursor: pointer;
      border-radius: 12px;
      border: 1px solid rgba(59, 130, 246, 0.2);
      object-fit: cover;
      transition: all 0.3s ease;
      background: rgba(30, 41, 59, 0.8);
      backdrop-filter: blur(10px);

      &:hover {
        border-color: rgba(59, 130, 246, 0.5);
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
        transform: translateY(-1px);
      }
    }
  }

  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    position: relative;
    z-index: 2;

    :deep(.el-checkbox) {
      .el-checkbox__label {
        color: #94a3b8;
        font-size: 14px;
      }

      .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: #3b82f6;
        border-color: #3b82f6;
      }

      .el-checkbox__inner {
        border-color: rgba(59, 130, 246, 0.3);
        background: rgba(30, 41, 59, 0.8);

        &:hover {
          border-color: #3b82f6;
        }
      }
    }
  }

  .login-btn {
    width: 100%;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: 1px solid rgba(59, 130, 246, 0.3);
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    margin-top: 10px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
    overflow: hidden;
    box-shadow:
      0 4px 15px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    &:hover {
      background: linear-gradient(135deg, #2563eb, #1e40af);
      transform: translateY(-1px);
      box-shadow:
        0 8px 25px rgba(59, 130, 246, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
    }

    &.is-loading {
      opacity: 0.8;
      cursor: not-allowed;
    }
  }
}

.login-footer {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  color: rgba(148, 163, 184, 0.9);
  font-size: 12px;
  text-align: center;
  z-index: 10;
  background: rgba(15, 23, 42, 0.6);
  padding: 10px 20px;
  border-radius: 25px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(59, 130, 246, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* 添加动画关键帧 */
@keyframes pulse {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.1;
    transform: scale(1.05);
  }
}

@keyframes glow {
  0% {
    filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.5));
  }
  100% {
    filter: drop-shadow(0 0 16px rgba(59, 130, 246, 0.8));
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 为登录表单添加入场动画 */
.login-form {
  animation: slideInRight 0.8s ease-out;
}

@media (max-width: 768px) {
  .login-form-container {
    width: 100%;
    position: fixed;
    top: 50%;
    right: 0;
    left: 0;
    transform: translateY(-50%);
    padding: 20px;
  }

  .glass-panel {
    padding: 30px 20px;
    max-width: none;
    border-radius: 12px;
    margin: 0 auto;
  }

  .login-form {
    max-width: 320px;

    .captcha-input-group {
      flex-direction: column;
      gap: 12px;

      .captcha-code {
        width: 100%;
        height: 45px;
      }
    }

    .form-header {
      .logo-section {
        .system-name {
          font-size: 22px;
        }
      }

      .form-title {
        font-size: 20px;
      }
    }
  }

  .tech-grid {
    background-size: 30px 30px;
  }

  .floating-particles {
    .particle {
      display: none; /* 移动端隐藏粒子效果以提升性能 */
    }
  }
}
</style>
