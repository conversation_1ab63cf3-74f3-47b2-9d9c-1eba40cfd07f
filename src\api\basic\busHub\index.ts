import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { BusHubVO, BusHubForm, BusHubQuery } from '@/api/basic/busHub/types';

/**
 * 查询场站列表
 * @param query
 * @returns {*}
 */
export const listBusHub = (query?: BusHubQuery): AxiosPromise<BusHubVO[]> => {
  return request({
    url: '/biz/busHub/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询场站详细
 * @param id
 */
export const getBusHub = (id: string | number): AxiosPromise<BusHubVO> => {
  return request({
    url: '/biz/busHub/' + id,
    method: 'get'
  });
};

/**
 * 新增场站
 * @param data
 */
export const addBusHub = (data: BusHubForm) => {
  return request({
    url: '/biz/busHub',
    method: 'post',
    data: data
  });
};

/**
 * 修改场站
 * @param data
 */
export const updateBusHub = (data: BusHubForm) => {
  return request({
    url: '/biz/busHub',
    method: 'put',
    data: data
  });
};

/**
 * 删除场站
 * @param id
 */
export const delBusHub = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/busHub/' + id,
    method: 'delete'
  });
};
