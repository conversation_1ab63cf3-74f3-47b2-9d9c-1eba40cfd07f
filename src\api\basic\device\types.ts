export interface DeviceVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 设备名称
   */
  deviceName: string;

  /**
   * 设备编号
   */
  deviceNumber: string;

  /**
   * 设备类型(video:视频设备, gps:GPS设备)
   */
  deviceType: string;

  /**
   * 设备型号
   */
  deviceModel: string;

  /**
   * 制造商
   */
  manufacturer: string;

  /**
   * 终端号/序列号
   */
  serialNumber: string;

  /**
   * 安装位置
   */
  installPosition: string;

  /**
   * 安装日期
   */
  installDate: string;

  /**
   * 所属组织机构名称
   */
  deptName: string;

  /**
   * IP地址
   */
  ipAddress: string;

  /**
   * 端口号
   */
  port: string;

  /**
   * 设备状态(0:离线,1:在线)
   */
  status: string;

  /**
   * 最后在线时间
   */
  lastOnlineTime: string;

  /**
   * 通道配置(JSON格式)
   */
  channelConfig: string;

  /**
   * 备注信息
   */
  remark: string;

  /**
   * 组织机构id
   */
  deptId: string | number;

}

export interface DeviceForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 设备名称
   */
  deviceName?: string;

  /**
   * 设备编号
   */
  deviceNumber?: string;

  /**
   * 设备类型(video:视频设备, gps:GPS设备)
   */
  deviceType?: string;

  /**
   * 设备型号
   */
  deviceModel?: string;

  /**
   * 制造商
   */
  manufacturer?: string;

  /**
   * 终端号/序列号
   */
  serialNumber?: string;

  /**
   * 安装位置
   */
  installPosition?: string;

  /**
   * 安装日期
   */
  installDate?: string;

  /**
   * 所属组织机构名称
   */
  deptName?: string;

  /**
   * IP地址
   */
  ipAddress?: string;

  /**
   * 端口号
   */
  port?: string;

  /**
   * 设备状态(0:离线,1:在线)
   */
  status?: string;

  /**
   * 最后在线时间
   */
  lastOnlineTime?: string;

  /**
   * 通道配置(JSON格式)
   */
  channelConfig?: string;

  /**
   * 备注信息
   */
  remark?: string;

  /**
   * 组织机构id
   */
  deptId?: string | number;

}

export interface DeviceQuery extends PageQuery {

  /**
   * 设备名称
   */
  deviceName?: string;

  /**
   * 设备编号
   */
  deviceNumber?: string;

  /**
   * 设备类型(video:视频设备, gps:GPS设备)
   */
  deviceType?: string;

  /**
   * 设备型号
   */
  deviceModel?: string;

  /**
   * 制造商
   */
  manufacturer?: string;

  /**
   * 终端号/序列号
   */
  serialNumber?: string;

  /**
   * 安装位置
   */
  installPosition?: string;

  /**
   * 安装日期
   */
  installDate?: string;

  /**
   * 所属组织机构名称
   */
  deptName?: string;

  /**
   * IP地址
   */
  ipAddress?: string;

  /**
   * 端口号
   */
  port?: string;

  /**
   * 设备状态(0:离线,1:在线)
   */
  status?: string;

  /**
   * 最后在线时间
   */
  lastOnlineTime?: string;

  /**
   * 通道配置(JSON格式)
   */
  channelConfig?: string;

  /**
   * 组织机构id
   */
  deptId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}



