<template>
  <div class='device-management-fullscreen'>
    <el-row :gutter='20'>
      <!-- 组织机构树 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <Monitor />
              </el-icon>
              <span>组织机构筛选</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入组织机构名称' prefix-icon='Search' clearable />
            </div>
            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            />
          </div>
        </div>
      </el-col>
      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 顶部工具栏 -->
          <header class='content-header'>
            <div class='header-left'>
              <div class='title-section'>
                <el-icon class='title-icon'>
                  <Monitor />
                </el-icon>
                <h1 class='page-title'>设备管理</h1>
              </div>
            </div>
          </header>

          <!-- 搜索表单 -->
          <div class='search-section' v-show='showSearch'>
            <el-form :model='queryParams' ref='queryFormRef' :inline='true' label-width='68px'>
              <el-form-item label='终端号' prop='serialNumber'>
                <el-input
                  v-model='queryParams.serialNumber'
                  placeholder='请输入终端号'
                  clearable
                  @keyup.enter='handleQuery'
                />
              </el-form-item>
              <el-form-item label='设备名称' prop='deviceName'>
                <el-input
                  v-model='queryParams.deviceName'
                  placeholder='请输入设备名称'
                  clearable
                  @keyup.enter='handleQuery'
                />
              </el-form-item>
              <el-form-item label='设备类型' prop='deviceType'>
                <el-select v-model='queryParams.deviceType' placeholder='请选择设备类型' clearable>
                  <el-option label='视频设备' value='video' />
                  <el-option label='GPS设备' value='gps' />
                </el-select>
              </el-form-item>
              <el-form-item label='状态' prop='status'>
                <el-select v-model='queryParams.status' placeholder='请选择状态' clearable>
                  <el-option label='在线' value='1' />
                  <el-option label='离线' value='0' />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type='primary' icon='Search' @click='handleQuery'>搜索</el-button>
                <el-button icon='Refresh' @click='resetQuery'>重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 操作按钮区域 -->
          <div class='action-section'>
            <div class='action-buttons'>
              <el-button type='primary' plain icon='Plus' @click='handleAdd'>新增</el-button>
              <el-button type='success' plain icon='Edit' :disabled='single' @click='handleUpdate'>修改</el-button>
              <el-button type='danger' plain icon='Delete' :disabled='multiple' @click='handleDelete'>删除</el-button>
              <el-button type='success' plain icon='Refresh' @click='handleStatusSync'>状态同步</el-button>
            </div>
            <right-toolbar v-model:showSearch='showSearch' @queryTable='getList'></right-toolbar>
          </div>

          <!-- 设备卡片列表 -->
          <div class='device-cards-container' v-loading='loading'>
            <div class='cards-grid'>
              <div
                v-for='device in deviceList'
                :key='device.deviceId'
                class='device-card'
                :class="{ 'selected': selectedDevices.includes(device.deviceId) }"
                @click='toggleSelection(device)'
              >
                <!-- 卡片头部 -->
                <div class='card-header'>
                  <div class='device-info'>
                    <div class='device-name'>{{ device.deviceName }}</div>
                    <div class='device-number'>{{ device.deviceNumber }}</div>
                  </div>
                  <div class='status-badge'>
                    <el-tag
                      v-if="device.status === '1'"
                      type='success'
                      size='small'
                      effect='dark'
                    >在线
                    </el-tag>
                    <el-tag
                      v-else
                      type='warning'
                      size='small'
                      effect='dark'
                    >离线
                    </el-tag>
                  </div>
                </div>

                <!-- 卡片内容 -->
                <div class='card-content'>
                  <div class='device-image'>
                    <el-icon size='36' :color='getDeviceIconColor(device.deviceType)'>
                      <component :is='getDeviceIcon(device.deviceType)' />
                    </el-icon>
                  </div>

                  <div class='device-details'>
                    <div class='detail-item'>
                      <span class='label'>设备类型:</span>
                      <span class='value'>{{ getDeviceTypeText(device.deviceType) }}</span>
                    </div>
                    <div class='detail-item'>
                      <span class='label'>所属组织机构:</span>
                      <span class='value'>{{ device.deptName }}</span>
                    </div>
                    <div class='detail-item'>
                      <span class='label'>安装位置:</span>
                      <span class='value'>{{ device.installPosition }}</span>
                    </div>
                    <div class='detail-item'>
                      <span class='label'>最后在线:</span>
                      <span class='value'>{{ device.lastOnlineTime }}</span>
                    </div>
                  </div>
                </div>

                <!-- 卡片操作按钮 -->
                <div class='card-actions'>
                  <el-button
                    type='primary'
                    size='small'
                    icon='Edit'
                    @click.stop='handleUpdate(device)'
                  >修改
                  </el-button>
                  <el-button
                    type='info'
                    size='small'
                    icon='View'
                    @click.stop='handleDetail(device)'
                  >详情
                  </el-button>
                  <el-button
                    type='danger'
                    size='small'
                    icon='Delete'
                    @click.stop='handleDelete(device)'
                  >删除
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if='!loading && deviceList.length === 0' class='empty-state'>
              <el-empty description='暂无设备数据' />
            </div>
          </div>

          <pagination
            v-show='total>0'
            :total='total'
            v-model:page='queryParams.pageNum'
            v-model:limit='queryParams.pageSize'
            @pagination='getList'
          />
        </div>

        <!-- 添加或修改设备对话框 -->
        <el-dialog
          :title='title'
          v-model='open'
          width='1000px'
          append-to-body
        >
          <el-form ref='deviceFormRef' :model='form' :rules='rules' label-width='100px'>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='设备名称' prop='deviceName'>
                  <el-input v-model='form.deviceName' placeholder='请输入设备名称' />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='设备编号' prop='deviceNumber'>
                  <el-input v-model='form.deviceNumber' placeholder='请输入设备编号' />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='设备类型' prop='deviceType'>
                  <el-radio-group v-model='form.deviceType' @change='handleDeviceTypeChange'>
                    <el-radio value='video'>视频设备</el-radio>
                    <el-radio value='gps'>GPS设备</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='设备型号' prop='deviceModel'>
                  <el-input v-model='form.deviceModel' placeholder='请输入设备型号' />
                </el-form-item>
              </el-col>
            </el-row>
            <!-- 视频设备通道配置 -->
            <el-row v-if="form.deviceType === 'video'">
              <el-col :span='24'>
                <el-form-item label='通道配置' prop='channelConfig'>
                  <div class='channel-config-container'>
                    <div class='channel-header'>
                      <span>通道列表</span>
                      <el-button type='primary' size='small' icon='Plus' @click='addChannel'>添加通道</el-button>
                    </div>
                    <div class='channel-table' v-if='form.channelConfig && form.channelConfig.length > 0'>
                      <el-table
                        :data='form.channelConfig'
                        style='width: 100%'
                        size='small'
                        class='channel-config-table'
                      >
                        <el-table-column label='通道名称' >
                          <template #default='{ row, $index }'>
                            <el-input
                              v-model='row.name'
                              placeholder='请输入通道名称'
                              size='small'
                            />
                          </template>
                        </el-table-column>
                        <el-table-column label='通道编号' >
                          <template #default='{ row, $index }'>
                            <el-input
                              v-model='row.number'
                              placeholder='如：CH01'
                              size='small'
                            />
                          </template>
                        </el-table-column>
                        <el-table-column label='分辨率' >
                          <template #default='{ row, $index }'>
                            <el-select
                              v-model='row.resolution'
                              placeholder='选择分辨率'
                              size='small'
                              style='width: 100%'
                            >
                              <el-option label='1920x1080' value='1920x1080' />
                              <el-option label='1280x720' value='1280x720' />
                              <el-option label='640x480' value='640x480' />
                            </el-select>
                          </template>
                        </el-table-column>
                        <el-table-column label='状态' width='100' align='center'>
                          <template #default='{ row, $index }'>
                            <el-switch
                              v-model='row.status'
                              size='small'
                              active-color='#13ce66'
                              inactive-color='#ff4949'
                            />
                          </template>
                        </el-table-column>
                        <el-table-column label='操作' width='80' align='center'>
                          <template #default='{ row, $index }'>
                            <el-button
                              type='danger'
                              size='small'
                              icon='Delete'
                              @click='removeChannel($index)'
                              circle
                            />
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                    <div v-else class='no-channels'>
                      <el-empty description='暂无通道配置' :image-size='60' />
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='制造商' prop='manufacturer'>
                  <el-input v-model='form.manufacturer' placeholder='请输入制造商' />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='终端号' prop='serialNumber'>
                  <el-input v-model='form.serialNumber' placeholder='请输入终端号' />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='安装位置' prop='installPosition'>
                  <el-input v-model='form.installPosition' placeholder='请输入安装位置' />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='安装日期' prop='installDate'>
                  <el-date-picker
                    v-model='form.installDate'
                    type='date'
                    placeholder='选择安装日期'
                    value-format='YYYY-MM-DD'
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='组织机构' prop='deptId'>
                  <el-tree-select
                    v-model='form.deptId'
                    :data='deptOptions'
                    :props="{ label: 'label', children: 'children', value: 'id' }"
                    placeholder='请选择所属组织机构'
                    check-strictly
                    :render-after-expand='false'
                    style='width: 100%'
                  />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='IP地址' prop='ipAddress'>
                  <el-input v-model='form.ipAddress' placeholder='请输入IP地址' />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='端口号' prop='port'>
                  <el-input-number style='width: 100%' v-model='form.port' :min='1' :max='65535' placeholder='请输入端口号' />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='状态' prop='status'>
                  <el-select v-model='form.status' placeholder='请选择状态'>
                    <el-option label='在线' value='1' />
                    <el-option label='离线' value='0' />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label='备注' prop='remark'>
              <el-input v-model='form.remark' type='textarea' placeholder='请输入内容' />
            </el-form-item>
          </el-form>
          <template #footer>
            <div class='dialog-footer'>
              <el-button type='primary' @click='submitForm'>确 定</el-button>
              <el-button @click='cancel'>取 消</el-button>
            </div>
          </template>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name='Device'>
import { listDevice, getDevice, delDevice, addDevice, updateDevice } from '@/api/basic/device';
import { deptTreeSelect } from '@/api/system/user';
import { Monitor, VideoCameraFilled, Position, WarnTriangleFilled } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

const deviceList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

// 卡片选择相关
const selectedDevices = ref([]);

// 组织机构树相关数据
const deptName = ref('');
const deptOptions = ref([]);
const deptTreeRef = ref();

// 表单引用
const queryFormRef = ref();
const deviceFormRef = ref();

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 25,
    deviceNumber: null,
    deviceName: null,
    deviceType: null,
    status: null,
    deptId: null
  },
  rules: {
    deviceName: [
      { required: true, message: '设备名称不能为空', trigger: 'blur' }
    ],
    deviceNumber: [
      { required: true, message: '设备编号不能为空', trigger: 'blur' }
    ],
    serialNumber: [
      { required: true, message: '终端号不能为空', trigger: 'change' }
    ],
    deptId: [
      { required: true, message: '组织机构不能为空', trigger: 'change' }
    ],
    deviceType: [
      { required: true, message: '设备类型不能为空', trigger: 'change' }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 组织机构树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** 查询组织机构下拉树结构 */
async function getTreeSelect() {
  try {
    const res = await deptTreeSelect();
    deptOptions.value = res.data;
  } catch (error) {
    console.error('获取组织机构树失败:', error);
    // 模拟组织机构数据
    deptOptions.value = [
      {
        id: 1,
        label: '总公司',
        children: [
          {
            id: 2,
            label: '运营部',
            children: [
              { id: 3, label: '第一车队' },
              { id: 4, label: '第二车队' }
            ]
          },
          {
            id: 5,
            label: '技术部',
            children: [
              { id: 6, label: '信息中心' },
              { id: 7, label: '监控中心' }
            ]
          }
        ]
      }
    ];
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  handleQuery();
}

/** 获取设备图标 */
function getDeviceIcon(deviceType) {
  const iconMap = {
    'gps': 'Position',
    'video': 'VideoCameraFilled'
  };
  return iconMap[deviceType] || 'Monitor';
}

/** 获取设备图标颜色 */
function getDeviceIconColor(deviceType) {
  const colorMap = {
    'gps': '#67C23A',
    'video': '#F56C6C'
  };
  return colorMap[deviceType] || '#409EFF';
}

/** 获取设备类型文本 */
function getDeviceTypeText(type) {
  const typeMap = {
    'gps': 'GPS设备',
    'video': '视频设备'
  };
  return typeMap[type] || type;
}

/** 查询设备列表 */
function getList() {
  loading.value = true;
  listDevice(queryParams.value).then(response => {
    const rows = response.rows || [];
    deviceList.value = rows.map(device => ({
      ...device,
      deviceId: device.id || device.deviceId,
      channelConfig: device.channelConfig ?
        (typeof device.channelConfig === 'string' ? JSON.parse(device.channelConfig) : device.channelConfig)
        : []
    }));
    total.value = response.total || 0;
    loading.value = false;
  }).catch(error => {
    console.error('获取设备列表失败:', error);
    loading.value = false;
    deviceList.value = [];
    total.value = 0;
    proxy.$modal.msgError('获取设备列表失败，请稍后重试');
  });
}

// 其他方法实现...
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  queryParams.value.deptId = null;
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.deviceId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 卡片选择切换 */
function toggleSelection(device) {
  const index = selectedDevices.value.indexOf(device.deviceId);
  if (index > -1) {
    selectedDevices.value.splice(index, 1);
  } else {
    selectedDevices.value.push(device.deviceId);
  }

  // 更新选择状态
  ids.value = selectedDevices.value;
  single.value = selectedDevices.value.length !== 1;
  multiple.value = selectedDevices.value.length === 0;
}

function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加设备';
}

function handleUpdate(row) {
  reset();
  const deviceId = row.deviceId || row.id;
  getDevice(deviceId).then(response => {
    const deviceData = response.data || response;
    form.value = {
      ...deviceData,
      deviceId: deviceData.id || deviceData.deviceId,
      channelConfig: deviceData.channelConfig ?
        (typeof deviceData.channelConfig === 'string' ? JSON.parse(deviceData.channelConfig) : deviceData.channelConfig)
        : []
    };
    open.value = true;
    title.value = '修改设备';
  }).catch(error => {
    console.error('获取设备详情失败:', error);
    proxy.$modal.msgError('获取设备详情失败，请稍后重试');
  });
}

function handleDetail(row) {
  proxy.$router.push({
    path: '/basic/device/detail',
    query: { deviceId: row.deviceId }
  });
}

function handleDelete(row) {
  const deviceIds = row.deviceId || row.id || ids.value;
  proxy.$modal.confirm('是否确认删除设备编号为"' + deviceIds + '"的数据项？').then(function() {
    delDevice(deviceIds).then(response => {
      proxy.$modal.msgSuccess('删除成功');
      getList();
    }).catch(error => {
      console.error('删除设备失败:', error);
      proxy.$modal.msgError('删除失败，请稍后重试');
    });
  }).catch(() => {
  });
}

function handleStatusSync() {
  proxy.$modal.msgSuccess('状态同步完成');
  getList();
}

function submitForm() {
  deviceFormRef.value?.validate(valid => {
    if (valid) {
      if (form.value.deviceId != null) {
        // 修改
        const updateData = {
          ...form.value,
          id: form.value.deviceId,
          channelConfig: JSON.stringify(form.value.channelConfig || [])
        };
        updateDevice(updateData).then(response => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        }).catch(error => {
          console.error('修改设备失败:', error);
          proxy.$modal.msgError('修改失败，请稍后重试');
        });
      } else {
        // 新增
        const addData = {
          ...form.value,
          channelConfig: JSON.stringify(form.value.channelConfig || [])
        };
        addDevice(addData).then(response => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        }).catch(error => {
          console.error('新增设备失败:', error);
          proxy.$modal.msgError('新增失败，请稍后重试');
        });
      }
    }
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    deviceId: null,
    deviceName: null,
    deviceNumber: null,
    deviceType: null,
    deviceModel: null,
    manufacturer: null,
    serialNumber: null,
    installPosition: null,
    installDate: null,
    deptId: null,
    ipAddress: null,
    port: null,
    status: '1',
    remark: null,
    channelConfig: []
  };
  deviceFormRef.value?.resetFields();
  deviceFormRef.value?.clearValidate();
}

/** 设备类型变化处理 */
function handleDeviceTypeChange(value) {
  if (value === 'video') {
    // 如果切换到视频设备，初始化通道配置
    if (!form.value.channelConfig || form.value.channelConfig.length === 0) {
      form.value.channelConfig = [];
    }
  } else {
    // 如果切换到GPS设备，清除通道配置
    form.value.channelConfig = [];
  }
}

/** 添加通道 */
function addChannel() {
  if (!form.value.channelConfig) {
    form.value.channelConfig = [];
  }
  form.value.channelConfig.push({
    name: '',
    number: '',
    resolution: '1920x1080',
    status: true
  });
}

/** 移除通道 */
function removeChannel(index) {
  if (form.value.channelConfig && index >= 0 && index < form.value.channelConfig.length) {
    form.value.channelConfig.splice(index, 1);
  }
}

getList();
getTreeSelect();
</script>

<style scoped>
/* 全屏布局基础样式 - 科技感深色主题 */
.device-management-fullscreen {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 左侧面板样式 */
.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.search-box {
  margin-bottom: 16px;
}

.dept-tree {
  background: transparent;
  color: #cbd5e1;
}

/* 右侧内容区域 */
.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 顶部工具栏 */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 24px;
  color: #60a5fa;
}

.page-title {
  font-size: 20px;
  font-weight: 700;
  color: #f8fafc;
  margin: 0;
}



/* 搜索区域 */
.search-section {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 设备卡片容器 */
.device-cards-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 400px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

/* 设备卡片样式 - 科技感设计 */
.device-card {
  background: linear-gradient(145deg,
  rgba(30, 41, 59, 0.9) 0%,
  rgba(51, 65, 85, 0.8) 100%);
  border: 2px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3),
  inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.device-card:hover {
  transform: translateY(-4px);
  border-color: rgba(96, 165, 250, 0.4);
  box-shadow: 0 12px 40px rgba(96, 165, 250, 0.2),
  0 0 30px rgba(96, 165, 250, 0.1),
  inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.device-card.selected {
  border-color: #22c55e;
  background: linear-gradient(145deg,
  rgba(34, 197, 94, 0.15) 0%,
  rgba(30, 41, 59, 0.9) 50%,
  rgba(51, 65, 85, 0.8) 100%);
  box-shadow: 0 0 30px rgba(34, 197, 94, 0.3),
  0 8px 25px rgba(0, 0, 0, 0.3),
  inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 科技感装饰线 */
.device-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg,
  transparent 0%,
  rgba(96, 165, 250, 0.6) 50%,
  transparent 100%);
}

.device-card.selected::before {
  background: linear-gradient(90deg,
  transparent 0%,
  rgba(34, 197, 94, 0.8) 50%,
  transparent 100%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 16px;
  font-weight: bold;
  color: #f8fafc;
  margin-bottom: 4px;
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.3);
}

.device-number {
  font-size: 12px;
  color: #94a3b8;
  font-weight: 500;
}

.status-badge {
  margin-left: 8px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.device-image {
  text-align: center;
  padding: 16px 0;
  background: linear-gradient(135deg,
  rgba(96, 165, 250, 0.1) 0%,
  rgba(59, 130, 246, 0.05) 100%);
  border-radius: 12px;
  border: 1px solid rgba(96, 165, 250, 0.2);
}

.device-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: linear-gradient(135deg,
  rgba(15, 23, 42, 0.8) 0%,
  rgba(30, 41, 59, 0.6) 100%);
  border-radius: 8px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  font-size: 13px;
}

.detail-item .label {
  color: #94a3b8;
  font-weight: 500;
  min-width: 80px;
}

.detail-item .value {
  color: #f8fafc;
  font-weight: 600;
  text-align: right;
  flex: 1;
  margin-left: 8px;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid rgba(147, 197, 253, 0.2);
}

.card-actions .el-button {
  flex: 1;
  font-size: 12px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 600;
  border-width: 1px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 0;
  color: #94a3b8;
}

/* 通道配置样式 */
.channel-config-container {
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  padding: 16px;
  background: rgba(15, 23, 42, 0.5);
  width: 100%;
}

.channel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  width: 100%;
}

.channel-header span {
  font-weight: 600;
  color: #f8fafc;
  font-size: 14px;
}

.channel-table {
  width: 100%;
}

.channel-config-table {
  background: rgba(30, 41, 59, 0.6);
  border-radius: 6px;
  overflow: hidden;
}

.channel-config-table .el-table__header {
  background: rgba(51, 65, 85, 0.8);
}

.channel-config-table .el-table__header th {
  background: rgba(51, 65, 85, 0.8);
  color: #f8fafc;
  font-weight: 600;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
}

.channel-config-table .el-table__body tr {
  background: rgba(30, 41, 59, 0.6);
}

.channel-config-table .el-table__body tr:hover {
  background: rgba(51, 65, 85, 0.6);
}

.channel-config-table .el-table__body td {
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
  padding: 8px;
}

.channel-config-table .el-input__inner,
.channel-config-table .el-select .el-input__inner {
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(147, 197, 253, 0.2);
  color: #f8fafc;
}

.channel-config-table .el-input__inner:focus,
.channel-config-table .el-select .el-input__inner:focus {
  border-color: rgba(96, 165, 250, 0.4);
}

.no-channels {
  text-align: center;
  padding: 20px;
  color: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-management-fullscreen {
    padding: 10px;
  }

  .header-left {
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }



  .cards-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .channel-config-table {
    font-size: 12px;
  }

  .channel-config-table .el-table__header th {
    padding: 8px 4px;
    font-size: 12px;
  }

  .channel-config-table .el-table__body td {
    padding: 4px;
  }

  .channel-config-table .el-input,
  .channel-config-table .el-select {
    font-size: 12px;
  }
}
</style>
