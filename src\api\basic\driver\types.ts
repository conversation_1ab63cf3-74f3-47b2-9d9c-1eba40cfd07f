export interface DriverVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 工号
   */
  employeeId: string | number;

  /**
   * 卡号
   */
  cardNumber: string;

  /**
   * 司机姓名
   */
  driverName: string;

  /**
   * 性别(0:女,1:男)
   */
  gender: string;

  /**
   * 年龄
   */
  age: string;

  /**
   * 联系电话
   */
  phone: string;

  /**
   * 身份证号
   */
  idCard: string | number;

  /**
   * 驾驶证号
   */
  licenseNumber: string;

  /**
   * 准驾车型
   */
  licenseType: string;

  /**
   * 所属机构名称
   */
  deptName: string;

  /**
   * 入职时间
   */
  hireDate: string;

  /**
   * 在职状态(0:离职,1:在职)
   */
  status: string;

  /**
   * 头像
   */
  avatar: string;

  /**
   * 备注内容
   */
  remark: string;

  /**
   * 组织机构id
   */
  deptId: string | number;

}

export interface DriverForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 工号
   */
  employeeId?: string | number;

  /**
   * 卡号
   */
  cardNumber?: string;

  /**
   * 司机姓名
   */
  driverName?: string;

  /**
   * 性别(0:女,1:男)
   */
  gender?: string;

  /**
   * 年龄
   */
  age?: string;

  /**
   * 联系电话
   */
  phone?: string;

  /**
   * 身份证号
   */
  idCard?: string | number;

  /**
   * 驾驶证号
   */
  licenseNumber?: string;

  /**
   * 准驾车型
   */
  licenseType?: string;

  /**
   * 所属机构名称
   */
  deptName?: string;

  /**
   * 入职时间
   */
  hireDate?: string;

  /**
   * 在职状态(0:离职,1:在职)
   */
  status?: string;

  /**
   * 头像
   */
  avatar?: string;

  /**
   * 备注内容
   */
  remark?: string;

  /**
   * 组织机构id
   */
  deptId?: string | number;

}

export interface DriverQuery extends PageQuery {

  /**
   * 工号
   */
  employeeId?: string | number;

  /**
   * 卡号
   */
  cardNumber?: string;

  /**
   * 司机姓名
   */
  driverName?: string;

  /**
   * 性别(0:女,1:男)
   */
  gender?: string;

  /**
   * 年龄
   */
  age?: string;

  /**
   * 联系电话
   */
  phone?: string;

  /**
   * 身份证号
   */
  idCard?: string | number;

  /**
   * 驾驶证号
   */
  licenseNumber?: string;

  /**
   * 准驾车型
   */
  licenseType?: string;

  /**
   * 所属机构名称
   */
  deptName?: string;

  /**
   * 入职时间
   */
  hireDate?: string;

  /**
   * 在职状态(0:离职,1:在职)
   */
  status?: string;

  /**
   * 头像
   */
  avatar?: string;

  /**
   * 组织机构id
   */
  deptId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}



