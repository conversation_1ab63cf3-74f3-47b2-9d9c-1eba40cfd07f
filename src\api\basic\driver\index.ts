import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DriverVO, DriverForm, DriverQuery } from './types';

/**
 * 查询司机列表
 * @param query
 * @returns {*}
 */
export const listDriver = (query?: DriverQuery): AxiosPromise<DriverVO[]> => {
  return request({
    url: '/biz/driver/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询司机详细
 * @param id
 */
export const getDriver = (id: string | number): AxiosPromise<DriverVO> => {
  return request({
    url: '/biz/driver/' + id,
    method: 'get'
  });
};

/**
 * 新增司机
 * @param data
 */
export const addDriver = (data: DriverForm) => {
  return request({
    url: '/biz/driver',
    method: 'post',
    data: data
  });
};

/**
 * 修改司机
 * @param data
 */
export const updateDriver = (data: DriverForm) => {
  return request({
    url: '/biz/driver',
    method: 'put',
    data: data
  });
};

/**
 * 删除司机
 * @param id
 */
export const delDriver = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/driver/' + id,
    method: 'delete'
  });
};

/**
 * 批量更新司机
 * @param data
 */
export const batchUpdateDriver = (data: any) => {
  return request({
    url: '/biz/driver/batchUpdate',
    method: 'put',
    data: data
  });
};


