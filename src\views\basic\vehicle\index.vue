<template>
  <div class='vehicle-management-fullscreen'>
    <el-row :gutter='20'>
      <!-- 组织机构树 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <Van />
              </el-icon>
              <span>组织机构筛选</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入组织机构名称' prefix-icon='Search' clearable />
            </div>
            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            />
          </div>
        </div>
      </el-col>
      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 顶部工具栏 -->
          <header class='content-header'>
            <div class='header-left'>
              <div class='title-section'>
                <el-icon class='title-icon'>
                  <Van />
                </el-icon>
                <h1 class='page-title'>车辆管理</h1>
              </div>
            </div>
          </header>

          <!-- 搜索表单 -->
          <div class='search-section' v-show='showSearch'>
            <el-form :model='queryParams' ref='queryFormRef' :inline='true' label-width='68px'>
              <el-form-item label='车牌号' prop='plateNumber'>
                <el-input
                  v-model='queryParams.plateNumber'
                  placeholder='请输入车牌号'
                  clearable
                  @keyup.enter='handleQuery'
                />
              </el-form-item>
              <el-form-item label='车辆编号' prop='vehicleNumber'>
                <el-input
                  v-model='queryParams.vehicleNumber'
                  placeholder='请输入车辆编号'
                  clearable
                  @keyup.enter='handleQuery'
                />
              </el-form-item>
              <el-form-item label='状态' prop='status'>
                <el-select v-model='queryParams.status' placeholder='请选择状态' clearable>
                  <el-option label='正常' value='1' />
                  <el-option label='维修' value='2' />
                  <el-option label='停用' value='0' />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type='primary' icon='Search' @click='handleQuery'>搜索</el-button>
                <el-button icon='Refresh' @click='resetQuery'>重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 操作按钮区域 -->
          <div class='action-section'>
            <div class='action-buttons'>
              <el-button type='primary' plain icon='Plus' @click='handleAdd'>新增</el-button>
              <el-button type='success' plain icon='Edit' :disabled='single' @click='handleUpdate'>修改</el-button>
              <el-button type='danger' plain icon='Delete' :disabled='multiple' @click='handleDelete'>删除</el-button>
              <el-button type='success' plain icon='Tools' @click='handleMaintenanceRecord'>维修保养</el-button>
            </div>
            <right-toolbar v-model:showSearch='showSearch' @queryTable='getList'></right-toolbar>
          </div>

          <!-- 车辆卡片列表 -->
          <div class='vehicle-cards-container' v-loading='loading'>
            <div class='cards-grid'>
              <div
                v-for='vehicle in vehicleList'
                :key='vehicle.vehicleId'
                class='vehicle-card'
                :class="{ 'selected': selectedVehicles.includes(vehicle.vehicleId) }"
                @click='toggleSelection(vehicle)'
              >
                <!-- 卡片头部 -->
                <div class='card-header'>
                  <div class='vehicle-info'>
                    <div class='plate-number'>{{ vehicle.plateNumber }}</div>
                  </div>
                  <div class='status-badge'>
                    <el-tag
                      v-if="vehicle.status === '1'"
                      type='success'
                      size='small'
                      effect='dark'
                    >正常
                    </el-tag>
                    <el-tag
                      v-else-if="vehicle.status === '2'"
                      type='warning'
                      size='small'
                      effect='dark'
                    >维修
                    </el-tag>
                    <el-tag
                      v-else
                      type='danger'
                      size='small'
                      effect='dark'
                    >停用
                    </el-tag>
                  </div>
                </div>

                <!-- 卡片内容 -->
                <div class='card-content'>
                  <div class='vehicle-image'>
                    <el-icon size='36' color='#409EFF'>
                      <Van />
                    </el-icon>
                  </div>

                  <div class='vehicle-details'>
                    <div class='detail-item'>
                      <span class='label'>车辆编号:</span>
                      <span class='value'>{{ vehicle.vehicleNumber }}</span>
                    </div>
                    <div class='detail-item'>
                      <span class='label'>车牌颜色:</span>
                      <span class='value'>{{ getPlateColorText(vehicle.plateColor) }}</span>
                    </div>
                    <div class='detail-item'>
                      <span class='label'>车辆类型:</span>
                      <span class='value'>{{ getVehicleTypeText(vehicle.vehicleType) }}</span>
                    </div>
                    <div class='detail-item'>
                      <span class='label'>上次维修:</span>
                      <span class='value'>{{ vehicle.lastMaintenanceTime || '暂无记录' }}</span>
                    </div>
                    <div class='detail-item'>
                      <span class='label'>所属组织机构:</span>
                      <span class='value'>{{ vehicle.deptName }}</span>
                    </div>
                  </div>
                </div>

                <!-- 卡片操作按钮 -->
                <div class='card-actions'>
                  <el-button
                    type='primary'
                    size='small'
                    icon='Edit'
                    @click.stop='handleUpdate(vehicle)'
                  >修改
                  </el-button>

                  <el-button
                    type='warning'
                    size='small'
                    icon='Clock'
                    @click.stop='handleTrackPlayback(vehicle)'
                  >轨迹
                  </el-button>
                  <el-button
                    type='danger'
                    size='small'
                    icon='Delete'
                    @click.stop='handleDelete(vehicle)'
                  >删除
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if='!loading && vehicleList.length === 0' class='empty-state'>
              <el-empty description='暂无车辆数据' />
            </div>
          </div>

          <pagination
            v-show='total>0'
            :total='total'
            v-model:page='queryParams.pageNum'
            v-model:limit='queryParams.pageSize'
            @pagination='getList'
          />
        </div>

        <!-- 添加或修改车辆对话框 -->
        <el-dialog
          :title='title'
          v-model='open'
          width='900px'
          append-to-body
        >
          <el-tabs v-model='activeTab' type='border-card'>
            <!-- 基础信息标签页 -->
            <el-tab-pane label='基础信息' name='basic'>
              <el-form ref='vehicleFormRef' :model='form' :rules='rules' label-width='100px'>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='车牌号' prop='plateNumber'>
                  <el-input v-model='form.plateNumber' placeholder='请输入车牌号' />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='车牌颜色' prop='plateColor'>
                  <el-select v-model='form.plateColor' placeholder='请选择车牌颜色'>
                    <el-option label='蓝牌' value='blue' />
                    <el-option label='黄牌' value='yellow' />
                    <el-option label='绿牌' value='green' />
                    <el-option label='白牌' value='white' />
                    <el-option label='黑牌' value='black' />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='车辆编号' prop='vehicleNumber'>
                  <el-input v-model='form.vehicleNumber' placeholder='请输入车辆编号' />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='状态' prop='status'>
                  <el-select v-model='form.status' placeholder='请选择状态'>
                    <el-option label='正常' value='1' />
                    <el-option label='维修' value='2' />
                    <el-option label='停用' value='0' />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='品牌型号' prop='brandModel'>
                  <el-input v-model='form.brandModel' placeholder='请输入品牌型号' />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='车辆类型' prop='vehicleType'>
                  <el-select v-model='form.vehicleType' placeholder='请选择车辆类型'>
                    <el-option label='公交车' value='bus' />
                    <el-option label='电动公交' value='electric_bus' />
                    <el-option label='混合动力' value='hybrid_bus' />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='座位数' prop='seatCount'>
                  <el-input-number v-model='form.seatCount' :min='10' :max='100' />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='车架号' prop='vinNumber'>
                  <el-input v-model='form.vinNumber' placeholder='请输入车架号' />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='购买日期' prop='purchaseDate'>
                  <el-date-picker
                    v-model='form.purchaseDate'
                    type='datetime'
                    placeholder='选择购买日期'
                    value-format='YYYY-MM-DD HH:mm:ss'
                    format='YYYY-MM-DD HH:mm:ss'
                  />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='上次维修时间' prop='lastMaintenanceTime'>
                  <el-date-picker
                    v-model='form.lastMaintenanceTime'
                    type='datetime'
                    placeholder='选择上次维修时间'
                    value-format='YYYY-MM-DD HH:mm:ss'
                    format='YYYY-MM-DD HH:mm:ss'
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='所属组织机构' prop='deptId'>
                  <el-tree-select
                    v-model='form.deptId'
                    :data='deptOptions'
                    :props="{ label: 'label', children: 'children', value: 'id' }"
                    placeholder='请选择所属组织机构'
                    check-strictly
                    :render-after-expand='false'
                    style='width: 100%'
                  />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='总里程(km)' prop='totalMileage'>
                  <el-input-number v-model='form.totalMileage' :min='0' />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label='备注' prop='remark'>
              <el-input v-model='form.remark' type='textarea' placeholder='请输入内容' />
            </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 设备绑定标签页 -->
            <el-tab-pane label='设备绑定' name='device'>
              <el-form ref='deviceFormRef' :model='form' :rules='rules' label-width='100px'>

            <!-- 主设备选择 -->
            <el-form-item label='主设备' prop='mainDeviceId'>
              <div class='device-selection-container'>
                <el-select
                  v-model='form.mainDeviceId'
                  placeholder='请选择主设备'
                  style='width: 300px; margin-right: 10px;'
                  @change='handleMainDeviceChange'
                >
                  <el-option
                    v-for='device in deviceOptions'
                    :key='device.id'
                    :label='`${device.name} (${device.code})`'
                    :value='device.id'
                  />
                </el-select>
                <el-button
                  v-if='form.mainDeviceId'
                  type='info'
                  size='small'
                  icon='View'
                  @click='viewDeviceDetail(form.mainDeviceId)'
                >
                  详情
                </el-button>
                <el-button
                  type='success'
                  size='small'
                  icon='Plus'
                  @click='createNewDevice()'
                >
                  新增设备
                </el-button>
              </div>
            </el-form-item>

            <!-- 子设备选择 -->
            <el-form-item label='子设备' prop='subDevices'>
              <div class='sub-devices-container'>
                <div
                  v-for='(deviceId, index) in form.subDevices'
                  :key='index'
                  class='sub-device-item'
                >
                  <div style='display: flex; align-items: center; gap: 10px; width: 100%;'>
                    <el-select
                      v-model='form.subDevices[index]'
                      placeholder='请选择子设备'
                      style='width: 300px;'
                    >
                      <el-option
                        v-for='device in deviceOptions'
                        :key='device.id'
                        :label='`${device.name} (${device.code})`'
                        :value='device.id'
                      />
                    </el-select>
                    <el-button
                      v-if='form.subDevices[index]'
                      type='info'
                      size='default'
                      icon='View'
                      @click='viewDeviceDetail(form.subDevices[index])'
                    >
                      详情
                    </el-button>
                    <el-button
                      type='danger'
                      size='default'
                      icon='Delete'
                      @click='removeSubDevice(index)'
                    >
                      移除
                    </el-button>
                  </div>
                </div>
                <el-button
                  type='primary'
                  size='small'
                  icon='Plus'
                  @click='addSubDevice'
                  style='margin-top: 10px;'
                >
                  添加子设备
                </el-button>
              </div>
            </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
          <template #footer>
            <div class='dialog-footer'>
              <el-button type='primary' @click='submitForm'>确 定</el-button>
              <el-button @click='cancel'>取 消</el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 新增设备对话框 -->
        <el-dialog
          title='新增设备'
          v-model='newDeviceOpen'
          width='600px'
          append-to-body
        >
          <el-form :model='newDeviceForm' :rules='newDeviceRules' ref='newDeviceFormRef' label-width='100px'>
            <el-form-item label='设备编号' prop='code'>
              <el-input v-model='newDeviceForm.code' placeholder='请输入设备编号' />
            </el-form-item>
            <el-form-item label='设备名称' prop='name'>
              <el-input v-model='newDeviceForm.name' placeholder='请输入设备名称' />
            </el-form-item>
            <el-form-item label='设备类型' prop='category'>
              <el-radio-group v-model='newDeviceForm.category' @change='handleNewDeviceTypeChange'>
                <el-radio value='video'>视频设备</el-radio>
                <el-radio value='gps'>GPS设备</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label='制造商' prop='manufacturer'>
              <el-input v-model='newDeviceForm.manufacturer' placeholder='请输入制造商' />
            </el-form-item>
            <el-form-item label='型号' prop='model'>
              <el-input v-model='newDeviceForm.model' placeholder='请输入设备型号' />
            </el-form-item>
            <el-form-item label='安装位置' prop='location'>
              <el-input v-model='newDeviceForm.location' placeholder='请输入安装位置' />
            </el-form-item>
            <el-form-item label='安装日期' prop='installDate'>
              <el-date-picker
                v-model='newDeviceForm.installDate'
                type='datetime'
                placeholder='选择安装日期'
                value-format='YYYY-MM-DD HH:mm:ss'
                format='YYYY-MM-DD HH:mm:ss'
              />
            </el-form-item>
            <el-form-item label='状态' prop='status'>
              <el-select v-model='newDeviceForm.status' placeholder='请选择状态'>
                <el-option label='正常' value='normal' />
                <el-option label='故障' value='fault' />
                <el-option label='维修中' value='maintenance' />
                <el-option label='停用' value='disabled' />
              </el-select>
            </el-form-item>
            <!-- 视频设备通道配置 -->
            <el-form-item v-if="newDeviceForm.category === 'video'" label='通道配置' prop='channelConfig'>
              <div class='channel-config-container'>
                <div class='channel-header'>
                  <span>通道列表</span>
                  <el-button type='primary' size='small' icon='Plus' @click='addNewDeviceChannel'>添加通道</el-button>
                </div>
                <div class='channel-table' v-if='newDeviceForm.channelConfig && newDeviceForm.channelConfig.length > 0'>
                  <el-table
                    :data='newDeviceForm.channelConfig'
                    style='width: 100%'
                    size='small'
                    class='channel-config-table'
                  >
                    <el-table-column label='通道名称'>
                      <template #default='{ row, $index }'>
                        <el-input
                          v-model='row.name'
                          placeholder='请输入通道名称'
                          size='small'
                        />
                      </template>
                    </el-table-column>
                    <el-table-column label='通道编号'>
                      <template #default='{ row, $index }'>
                        <el-input
                          v-model='row.number'
                          placeholder='如：CH01'
                          size='small'
                        />
                      </template>
                    </el-table-column>
                    <el-table-column label='分辨率'>
                      <template #default='{ row, $index }'>
                        <el-select
                          v-model='row.resolution'
                          placeholder='选择分辨率'
                          size='small'
                          style='width: 100%'
                        >
                          <el-option label='1920x1080' value='1920x1080' />
                          <el-option label='1280x720' value='1280x720' />
                          <el-option label='640x480' value='640x480' />
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column label='状态' width='100' align='center'>
                      <template #default='{ row, $index }'>
                        <el-switch
                          v-model='row.status'
                          size='small'
                          active-color='#13ce66'
                          inactive-color='#ff4949'
                        />
                      </template>
                    </el-table-column>
                    <el-table-column label='操作' width='80' align='center'>
                      <template #default='{ row, $index }'>
                        <el-button
                          type='danger'
                          size='small'
                          icon='Delete'
                          @click='removeNewDeviceChannel($index)'
                          circle
                        />
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <div v-else class='no-channels'>
                  <el-empty description='暂无通道配置' :image-size='60' />
                </div>
              </div>
            </el-form-item>
            <el-form-item label='备注' prop='remark'>
              <el-input v-model='newDeviceForm.remark' type='textarea' placeholder='请输入备注' />
            </el-form-item>
          </el-form>
          <template #footer>
            <div class='dialog-footer'>
              <el-button type='primary' @click='saveNewDevice'>保 存</el-button>
              <el-button @click='cancelNewDevice'>取 消</el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 设备详情对话框 -->
        <el-dialog
          title='设备详情'
          v-model='deviceDetailOpen'
          width='1000px'
          append-to-body
        >
          <el-form :model='currentDevice' label-width='100px'>
            <el-form-item label='设备编号'>
              <el-input v-model='currentDevice.code' placeholder='请输入设备编号' />
            </el-form-item>
            <el-form-item label='设备名称'>
              <el-input v-model='currentDevice.name' placeholder='请输入设备名称' />
            </el-form-item>
            <el-form-item label='设备类型'>
              <el-radio-group v-model='currentDevice.category' @change='handleCurrentDeviceTypeChange'>
                <el-radio value='video'>视频设备</el-radio>
                <el-radio value='gps'>GPS设备</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label='制造商'>
              <el-input v-model='currentDevice.manufacturer' placeholder='请输入制造商' />
            </el-form-item>
            <el-form-item label='型号'>
              <el-input v-model='currentDevice.model' placeholder='请输入设备型号' />
            </el-form-item>
            <el-form-item label='安装位置'>
              <el-input v-model='currentDevice.location' placeholder='请输入安装位置' />
            </el-form-item>
            <el-form-item label='安装日期'>
              <el-date-picker
                v-model='currentDevice.installDate'
                type='datetime'
                placeholder='选择安装日期'
                value-format='YYYY-MM-DD HH:mm:ss'
                format='YYYY-MM-DD HH:mm:ss'
              />
            </el-form-item>
            <el-form-item label='状态'>
              <el-select v-model='currentDevice.status' placeholder='请选择状态'>
                <el-option label='正常' value='normal' />
                <el-option label='故障' value='fault' />
                <el-option label='维修中' value='maintenance' />
                <el-option label='停用' value='disabled' />
              </el-select>
            </el-form-item>
            <!-- 视频设备通道配置 -->
            <el-form-item v-if="currentDevice.category === 'video'" label='通道配置'>
              <div class='channel-config-container'>
                <div class='channel-header'>
                  <span>通道列表</span>
                  <el-button type='primary' size='small' icon='Plus' @click='addCurrentDeviceChannel'>添加通道</el-button>
                </div>
                <div class='channel-table' v-if='currentDevice.channelConfig && currentDevice.channelConfig.length > 0'>
                  <el-table
                    :data='currentDevice.channelConfig'
                    style='width: 100%'
                    size='small'
                    class='channel-config-table'
                  >
                    <el-table-column label='通道名称'>
                      <template #default='{ row, $index }'>
                        <el-input
                          v-model='row.name'
                          placeholder='请输入通道名称'
                          size='small'
                        />
                      </template>
                    </el-table-column>
                    <el-table-column label='通道编号'>
                      <template #default='{ row, $index }'>
                        <el-input
                          v-model='row.number'
                          placeholder='如：CH01'
                          size='small'
                        />
                      </template>
                    </el-table-column>
                    <el-table-column label='分辨率'>
                      <template #default='{ row, $index }'>
                        <el-select
                          v-model='row.resolution'
                          placeholder='选择分辨率'
                          size='small'
                          style='width: 100%'
                        >
                          <el-option label='1920x1080' value='1920x1080' />
                          <el-option label='1280x720' value='1280x720' />
                          <el-option label='640x480' value='640x480' />
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column label='状态' width='100' align='center'>
                      <template #default='{ row, $index }'>
                        <el-switch
                          v-model='row.status'
                          size='small'
                          active-color='#13ce66'
                          inactive-color='#ff4949'
                        />
                      </template>
                    </el-table-column>
                    <el-table-column label='操作' width='80' align='center'>
                      <template #default='{ row, $index }'>
                        <el-button
                          type='danger'
                          size='small'
                          icon='Delete'
                          @click='removeCurrentDeviceChannel($index)'
                          circle
                        />
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <div v-else class='no-channels'>
                  <el-empty description='暂无通道配置' :image-size='60' />
                </div>
              </div>
            </el-form-item>
            <el-form-item label='备注'>
              <el-input v-model='currentDevice.remark' type='textarea' placeholder='请输入备注' />
            </el-form-item>
          </el-form>
          <template #footer>
            <div class='dialog-footer'>
              <el-button type='primary' @click='saveDeviceDetail'>保 存</el-button>
              <el-button @click='deviceDetailOpen = false'>取 消</el-button>
            </div>
          </template>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name='Vehicle'>
import { listVehicle, getVehicle, delVehicle, addVehicle, updateVehicle } from '@/api/basic/vehicle';
import { deptTreeSelect } from '@/api/system/user';
import { Van } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

const vehicleList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const activeTab = ref('basic');

// 卡片选择相关
const selectedVehicles = ref([]);

// 组织机构树相关数据
const deptName = ref('');
const deptOptions = ref([]);
const deptTreeRef = ref();

// 设备相关数据
const deviceOptions = ref([]);
const deviceDetailOpen = ref(false);
const currentDevice = ref({});
const newDeviceOpen = ref(false);
const newDeviceForm = ref({});
const newDeviceFormRef = ref();

// 表单引用
const queryFormRef = ref();
const vehicleFormRef = ref();
const deviceFormRef = ref();

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 25,
    plateNumber: null,
    vehicleNumber: null,
    status: null,
    deptId: null
  },
  rules: {
    plateNumber: [
      { required: true, message: '车牌号不能为空', trigger: 'blur' }
    ],
    plateColor: [
      { required: true, message: '车牌颜色不能为空', trigger: 'change' }
    ],
    mainDeviceId: [
      { required: true, message: '请选择主设备', trigger: 'change' }
    ]
  },
  newDeviceRules: {
    code: [
      { required: true, message: '设备编号不能为空', trigger: 'blur' }
    ],
    name: [
      { required: true, message: '设备名称不能为空', trigger: 'blur' }
    ],
    status: [
      { required: true, message: '请选择设备状态', trigger: 'change' }
    ]
  }
});

const { queryParams, form, rules, newDeviceRules } = toRefs(data);

/** 组织机构树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** 查询组织机构下拉树结构 */
async function getTreeSelect() {
  try {
    const res = await deptTreeSelect();
    deptOptions.value = res.data;
  } catch (error) {
    console.error('获取组织机构树失败:', error);
    // 模拟组织机构数据
    deptOptions.value = [
      {
        id: 1,
        label: '总公司',
        children: [
          {
            id: 2,
            label: '运营部',
            children: [
              { id: 3, label: '第一车队' },
              { id: 4, label: '第二车队' }
            ]
          },
          {
            id: 5,
            label: '维修部',
            children: [
              { id: 6, label: '机修组' },
              { id: 7, label: '电修组' }
            ]
          }
        ]
      }
    ];
  }
}

/** 初始化设备数据 */
function initDeviceOptions() {
  deviceOptions.value = [
    {
      id: 'DEV001',
      code: 'GPS-MAIN-001',
      name: 'GPS主控制器-宇通专用',
      category: 'gps',
      manufacturer: '海康威视',
      model: 'HK-GPS-M100',
      location: '驾驶室仪表台',
      installDate: '2023-01-15',
      status: 'normal',
      remark: '主控制器，负责车辆定位和数据传输',
      channelConfig: []
    },
    {
      id: 'DEV002',
      code: 'GPS-MAIN-002',
      name: 'GPS主控制器-比亚迪专用',
      category: 'gps',
      manufacturer: '大华技术',
      model: 'DH-GPS-M200',
      location: '驾驶室仪表台',
      installDate: '2023-02-10',
      status: 'normal',
      remark: '电动车专用主控制器',
      channelConfig: []
    },
    {
      id: 'DEV003',
      code: 'GPS-MAIN-003',
      name: 'GPS主控制器-金龙专用',
      category: 'gps',
      manufacturer: '中兴通讯',
      model: 'ZTE-GPS-M300',
      location: '驾驶室仪表台',
      installDate: '2023-03-05',
      status: 'maintenance',
      remark: '正在维修中',
      channelConfig: []
    },
    {
      id: 'DEV004',
      code: 'VIDEO-001',
      name: '车载视频监控-前置',
      category: 'video',
      manufacturer: '海康威视',
      model: 'HK-VIDEO-F100',
      location: '前挡风玻璃',
      installDate: '2023-01-15',
      status: 'normal',
      remark: '前置视频监控',
      channelConfig: [
        { name: '前置摄像头', number: 'CH01', resolution: '1920x1080', status: true }
      ]
    },
    {
      id: 'DEV005',
      code: 'VIDEO-002',
      name: '车载视频监控-后置',
      category: 'video',
      manufacturer: '海康威视',
      model: 'HK-VIDEO-R100',
      location: '后挡风玻璃',
      installDate: '2023-01-15',
      status: 'normal',
      remark: '后置视频监控',
      channelConfig: [
        { name: '后置摄像头', number: 'CH01', resolution: '1280x720', status: true }
      ]
    },
    {
      id: 'DEV006',
      code: 'GPS-002',
      name: 'GPS定位终端-002',
      category: 'gps',
      manufacturer: '博世',
      model: 'BOSCH-GPS-S100',
      location: '仪表台',
      installDate: '2023-01-20',
      status: 'normal',
      remark: 'GPS定位设备',
      channelConfig: []
    },
    {
      id: 'DEV007',
      code: 'GPS-003',
      name: 'GPS定位终端-003',
      category: 'gps',
      manufacturer: '霍尼韦尔',
      model: 'HON-GPS-S200',
      location: '驾驶室',
      installDate: '2023-02-01',
      status: 'normal',
      remark: 'GPS定位设备',
      channelConfig: []
    },
    {
      id: 'DEV008',
      code: 'VIDEO-003',
      name: '多路视频监控',
      category: 'video',
      manufacturer: '大华技术',
      model: 'DH-VIDEO-M100',
      location: '车厢内',
      installDate: '2023-02-15',
      status: 'normal',
      remark: '多路视频监控系统',
      channelConfig: [
        { name: '车厢前部', number: 'CH01', resolution: '1920x1080', status: true },
        { name: '车厢中部', number: 'CH02', resolution: '1280x720', status: true },
        { name: '车厢后部', number: 'CH03', resolution: '1280x720', status: false }
      ]
    },
    {
      id: 'DEV009',
      code: 'GPS-002',
      name: 'GPS定位器-备用',
      category: 'gps',
      manufacturer: '北斗星通',
      model: 'BD-GPS-B100',
      location: '车顶天线',
      installDate: '2023-03-01',
      status: 'normal',
      remark: '备用GPS定位设备',
      channelConfig: []
    }
  ];
}

/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  handleQuery();
}

/** 查询车辆列表 */
function getList() {
  loading.value = true;
  listVehicle(queryParams.value).then(response => {
    // 处理响应数据，转换subDeviceIds为subDevices数组
    const rows = response.rows || [];
    vehicleList.value = rows.map(vehicle => ({
      ...vehicle,
      vehicleId: vehicle.id || vehicle.vehicleId, // 映射id到vehicleId
      subDevices: vehicle.subDeviceIds ? vehicle.subDeviceIds.toString().split(',').filter(id => id.trim()) : []
    }));
    total.value = response.total || 0;
    loading.value = false;
  }).catch(error => {
    console.error('获取车辆列表失败:', error);
    loading.value = false;
    vehicleList.value = [];
    total.value = 0;
    proxy.$modal.msgError('获取车辆列表失败，请稍后重试');
  });
}

// 其他方法实现...
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  queryParams.value.deptId = null;
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.vehicleId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 卡片选择切换 */
function toggleSelection(vehicle) {
  const index = selectedVehicles.value.indexOf(vehicle.vehicleId);
  if (index > -1) {
    selectedVehicles.value.splice(index, 1);
  } else {
    selectedVehicles.value.push(vehicle.vehicleId);
  }

  // 更新选择状态
  ids.value = selectedVehicles.value;
  single.value = selectedVehicles.value.length !== 1;
  multiple.value = selectedVehicles.value.length === 0;
}

/** 获取车辆类型文本 */
function getVehicleTypeText(type) {
  const typeMap = {
    'bus': '公交车',
    'electric_bus': '电动公交',
    'hybrid_bus': '混合动力'
  };
  return typeMap[type] || type;
}

/** 获取车牌颜色文本 */
function getPlateColorText(color) {
  const colorMap = {
    'blue': '蓝牌',
    'yellow': '黄牌',
    'green': '绿牌',
    'white': '白牌',
    'black': '黑牌'
  };
  return colorMap[color] || color;
}

function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加车辆';
}

function handleUpdate(row) {
  reset();
  const vehicleId = row.vehicleId || row.id;
  if (vehicleId) {
    getVehicle(vehicleId).then(response => {
      const data = response.data;
      form.value = {
        ...data,
        vehicleId: data.id, // 映射id到vehicleId
        subDevices: data.subDeviceIds ? data.subDeviceIds.toString().split(',').filter(id => id.trim()) : ['']
      };
      open.value = true;
      title.value = '修改车辆';
    }).catch(error => {
      console.error('获取车辆详情失败:', error);
      // 如果API失败，使用传入的row数据
      form.value = { 
        ...row,
        subDevices: row.subDevices || ['']
      };
      open.value = true;
      title.value = '修改车辆';
    });
  } else {
    form.value = { 
      ...row,
      subDevices: row.subDevices || ['']
    };
    open.value = true;
    title.value = '修改车辆';
  }
}



function handleDelete(row) {
  const vehicleIds = row ? (row.vehicleId || row.id) : ids.value;
  const vehicleNames = row ? row.plateNumber : vehicleList.value
    .filter(v => ids.value.includes(v.vehicleId || v.id))
    .map(v => v.plateNumber)
    .join(', ');
    
  proxy.$modal.confirm(`是否确认删除车牌号为"${vehicleNames}"的车辆数据？`).then(function() {
    return delVehicle(vehicleIds);
  }).then(() => {
    proxy.$modal.msgSuccess('删除成功');
    getList();
  }).catch(error => {
    if (error !== 'cancel') {
      console.error('删除车辆失败:', error);
      proxy.$modal.msgError('删除失败');
    }
  });
}

function handleMaintenanceRecord() {
  proxy.$router.push('/basic/vehicle/maintenance');
}

// 轨迹回放相关方法
function handleTrackPlayback(vehicle) {
  // 跳转到轨迹回放页面，传递车辆信息
  proxy.$router.push({
    path: '/basic/vehicle/track-playback',
    query: {
      vehicleId: vehicle.vehicleId,
      plateNumber: vehicle.plateNumber,
      vehicleNumber: vehicle.vehicleNumber,
      vehicleType: vehicle.vehicleType
    }
  });
}

function submitForm() {
  // 验证基础信息表单
  vehicleFormRef.value?.validate(valid1 => {
    if (valid1) {
      // 验证设备表单
      deviceFormRef.value?.validate(valid2 => {
        if (valid2) {
          // 准备提交数据，转换subDevices数组为逗号分隔字符串
          const submitData = {
            ...form.value,
            id: form.value.vehicleId, // 映射vehicleId到id
            subDeviceIds: form.value.subDevices 
              ? form.value.subDevices.filter(id => id && id.trim()).join(',')
              : ''
          };
          // 删除前端使用的字段
          delete submitData.vehicleId;
          delete submitData.subDevices;
          
          if (form.value.vehicleId != null) {
            updateVehicle(submitData).then(response => {
              proxy.$modal.msgSuccess('修改成功');
              open.value = false;
              getList();
            }).catch(error => {
              console.error('修改车辆失败:', error);
              proxy.$modal.msgError('修改失败');
            });
          } else {
            addVehicle(submitData).then(response => {
              proxy.$modal.msgSuccess('新增成功');
              open.value = false;
              getList();
            }).catch(error => {
              console.error('新增车辆失败:', error);
              proxy.$modal.msgError('新增失败');
            });
          }
        } else {
          // 如果设备表单验证失败，切换到设备绑定tab
          activeTab.value = 'device';
        }
      });
    } else {
      // 如果基础信息表单验证失败，切换到基础信息tab
      activeTab.value = 'basic';
    }
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    vehicleId: null,
    plateNumber: null,
    plateColor: null,
    vehicleNumber: null,
    brandModel: null,
    vehicleType: null,
    seatCount: null,
    vinNumber: null,
    purchaseDate: null,
    lastMaintenanceTime: null,
    totalMileage: 0,
    mainDeviceId: null,
    subDevices: [''],
    deptId: null,
    status: '1',
    remark: null
  };
  activeTab.value = 'basic';
  vehicleFormRef.value?.resetFields();
  vehicleFormRef.value?.clearValidate();
  deviceFormRef.value?.resetFields();
  deviceFormRef.value?.clearValidate();
}

/** 添加子设备 */
function addSubDevice() {
  form.value.subDevices.push('');
}

/** 移除子设备 */
function removeSubDevice(index) {
  form.value.subDevices.splice(index, 1);
  // 如果移除后没有子设备了，添加一个空的子设备位置
  if (form.value.subDevices.length === 0) {
    form.value.subDevices.push('');
  }
}

/** 主设备变更处理 */
function handleMainDeviceChange(deviceId) {
  // 可以在这里添加主设备变更的逻辑
  console.log('主设备变更:', deviceId);
}

/** 查看设备详情 */
function viewDeviceDetail(deviceId) {
  const device = deviceOptions.value.find(d => d.id === deviceId);
  if (device) {
    // 深度复制设备信息，确保通道配置是响应式的
    currentDevice.value = {
      ...device,
      channelConfig: device.channelConfig ? [...device.channelConfig.map(ch => ({ ...ch }))] : []
    };
    console.log('Device detail opened:', currentDevice.value);
    console.log('Channel config:', currentDevice.value.channelConfig);
    deviceDetailOpen.value = true;
  }
}

/** 保存设备详情 */
function saveDeviceDetail() {
  const index = deviceOptions.value.findIndex(d => d.id === currentDevice.value.id);
  if (index !== -1) {
    deviceOptions.value[index] = { ...currentDevice.value };
    proxy.$modal.msgSuccess('设备信息保存成功');
  }
  deviceDetailOpen.value = false;
}

/** 创建新设备 */
function createNewDevice() {
  newDeviceForm.value = {
    code: '',
    name: '',
    category: 'gps',
    manufacturer: '',
    model: '',
    location: '',
    installDate: '',
    status: 'normal',
    remark: '',
    channelConfig: []
  };
  newDeviceOpen.value = true;
}



/** 保存新设备 */
function saveNewDevice() {
  newDeviceFormRef.value?.validate(valid => {
    if (valid) {
      // 生成新的设备ID
      const existingIds = deviceOptions.value
        .filter(d => d.id.startsWith('DEV'))
        .map(d => parseInt(d.id.slice(3)))
        .sort((a, b) => b - a);
      const newIdNumber = existingIds.length > 0 ? existingIds[0] + 1 : 1;
      const newDeviceId = `DEV${String(newIdNumber).padStart(3, '0')}`;

      // 创建新设备对象
      const newDevice = {
        id: newDeviceId,
        ...newDeviceForm.value
      };

      // 添加到设备列表
      deviceOptions.value.push(newDevice);

      proxy.$modal.msgSuccess('设备创建成功');
      newDeviceOpen.value = false;
    }
  });
}

/** 取消新设备创建 */
function cancelNewDevice() {
  newDeviceOpen.value = false;
  newDeviceForm.value = {};
  newDeviceFormRef.value?.resetFields();
}

/** 新增设备类型变化处理 */
function handleNewDeviceTypeChange(value) {
  if (value === 'video') {
    // 如果切换到视频设备，初始化通道配置
    if (!newDeviceForm.value.channelConfig || newDeviceForm.value.channelConfig.length === 0) {
      newDeviceForm.value.channelConfig = [];
    }
  } else {
    // 如果切换到GPS设备，清除通道配置
    newDeviceForm.value.channelConfig = [];
  }
}

/** 当前设备类型变化处理 */
function handleCurrentDeviceTypeChange(value) {
  console.log('Device type changed to:', value);
  if (value === 'video') {
    // 如果切换到视频设备，初始化通道配置
    if (!currentDevice.value.channelConfig || currentDevice.value.channelConfig.length === 0) {
      currentDevice.value.channelConfig = [];
    }
  } else {
    // 如果切换到GPS设备，清除通道配置
    currentDevice.value.channelConfig = [];
  }
  console.log('Updated device:', currentDevice.value);
}

/** 添加新设备通道 */
function addNewDeviceChannel() {
  if (!newDeviceForm.value.channelConfig) {
    newDeviceForm.value.channelConfig = [];
  }
  newDeviceForm.value.channelConfig.push({
    name: '',
    number: '',
    resolution: '1920x1080',
    status: true
  });
}

/** 移除新设备通道 */
function removeNewDeviceChannel(index) {
  if (newDeviceForm.value.channelConfig && index >= 0 && index < newDeviceForm.value.channelConfig.length) {
    newDeviceForm.value.channelConfig.splice(index, 1);
  }
}

/** 添加当前设备通道 */
function addCurrentDeviceChannel() {
  console.log('addCurrentDeviceChannel called, current device:', currentDevice.value);
  console.log('current channelConfig:', currentDevice.value.channelConfig);

  if (!currentDevice.value.channelConfig) {
    currentDevice.value.channelConfig = [];
  }

  const newChannel = {
    name: '',
    number: '',
    resolution: '1920x1080',
    status: true
  };

  currentDevice.value.channelConfig.push(newChannel);

  console.log('Channel added, new length:', currentDevice.value.channelConfig.length);
  console.log('Updated channelConfig:', currentDevice.value.channelConfig);
}

/** 移除当前设备通道 */
function removeCurrentDeviceChannel(index) {
  if (currentDevice.value.channelConfig && index >= 0 && index < currentDevice.value.channelConfig.length) {
    currentDevice.value.channelConfig.splice(index, 1);
  }
}

getList();
getTreeSelect();
initDeviceOptions();
</script>

<style scoped>
/* 全屏布局基础样式 - 科技感深色主题 */
.vehicle-management-fullscreen {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 左侧面板样式 */
.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.search-box {
  margin-bottom: 16px;
}

.dept-tree {
  background: transparent;
  color: #cbd5e1;
}

/* 右侧内容区域 */
.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 顶部工具栏 */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 24px;
  color: #60a5fa;
}

.page-title {
  font-size: 20px;
  font-weight: 700;
  color: #f8fafc;
  margin: 0;
}



/* 搜索区域 */
.search-section {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 车辆卡片容器 */
.vehicle-cards-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 400px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

/* 车辆卡片样式 - 科技感设计 */
.vehicle-card {
  background: linear-gradient(145deg,
  rgba(30, 41, 59, 0.9) 0%,
  rgba(51, 65, 85, 0.8) 100%);
  border: 2px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3),
  inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.vehicle-card:hover {
  transform: translateY(-4px);
  border-color: rgba(96, 165, 250, 0.4);
  box-shadow: 0 12px 40px rgba(96, 165, 250, 0.2),
  0 0 30px rgba(96, 165, 250, 0.1),
  inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.vehicle-card.selected {
  border-color: #22c55e;
  background: linear-gradient(145deg,
  rgba(34, 197, 94, 0.15) 0%,
  rgba(30, 41, 59, 0.9) 50%,
  rgba(51, 65, 85, 0.8) 100%);
  box-shadow: 0 0 30px rgba(34, 197, 94, 0.3),
  0 8px 25px rgba(0, 0, 0, 0.3),
  inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 科技感装饰线 */
.vehicle-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg,
  transparent 0%,
  rgba(96, 165, 250, 0.6) 50%,
  transparent 100%);
}

.vehicle-card.selected::before {
  background: linear-gradient(90deg,
  transparent 0%,
  rgba(34, 197, 94, 0.8) 50%,
  transparent 100%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
}

.vehicle-info {
  flex: 1;
}

.plate-number {
  font-size: 18px;
  font-weight: bold;
  color: #f8fafc;
  margin-bottom: 4px;
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.3);
}

.status-badge {
  margin-left: 8px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.vehicle-image {
  text-align: center;
  padding: 16px 0;
  background: linear-gradient(135deg,
  rgba(96, 165, 250, 0.1) 0%,
  rgba(59, 130, 246, 0.05) 100%);
  border-radius: 12px;
  border: 1px solid rgba(96, 165, 250, 0.2);
}

.vehicle-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: linear-gradient(135deg,
  rgba(15, 23, 42, 0.8) 0%,
  rgba(30, 41, 59, 0.6) 100%);
  border-radius: 8px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  font-size: 13px;
}

.detail-item .label {
  color: #94a3b8;
  font-weight: 500;
  min-width: 80px;
}

.detail-item .value {
  color: #f8fafc;
  font-weight: 600;
  text-align: right;
  flex: 1;
  margin-left: 8px;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  gap: 6px;
  padding-top: 16px;
  border-top: 1px solid rgba(147, 197, 253, 0.2);
}

.card-actions .el-button {
  flex: 1;
  font-size: 11px;
  padding: 6px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-weight: 600;
  border-width: 1px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
  min-width: 0;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 0;
  color: #94a3b8;
}

/* 设备绑定样式 - 暗色科技风格 */
.sub-devices-container {
  background: linear-gradient(145deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.8) 100%);
  border: 2px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  width: 100%;
  min-width: 500px;
}

/* 科技感装饰线 */
.sub-devices-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(96, 165, 250, 0.6) 50%,
    transparent 100%);
}

.sub-device-item {
  margin-bottom: 16px;
  padding: 16px;
  background: linear-gradient(145deg,
    rgba(30, 41, 59, 0.9) 0%,
    rgba(51, 65, 85, 0.8) 100%);
  border: 2px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.sub-device-item:last-child {
  margin-bottom: 0;
}

/* 子设备项装饰线 */
.sub-device-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(147, 197, 253, 0.4) 50%,
    transparent 100%);
}

/* 设备选择容器样式 */
.device-selection-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vehicle-management-fullscreen {
    padding: 10px;
  }

  .header-left {
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }



  .cards-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .sub-device-item .el-col {
    margin-bottom: 8px;
  }

  .channel-config-table {
    font-size: 12px;
  }

  .channel-config-table .el-table__header th {
    padding: 8px 4px;
    font-size: 12px;
  }

  .channel-config-table .el-table__body td {
    padding: 4px;
  }

  .channel-config-table .el-input,
  .channel-config-table .el-select {
    font-size: 12px;
  }
}

/* 通道配置样式 */
.channel-config-container {
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  padding: 16px;
  background: rgba(15, 23, 42, 0.5);
  width: 100%;
}

.channel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  width: 100%;
}

.channel-header span {
  font-weight: 600;
  color: #f8fafc;
  font-size: 14px;
}

.channel-table {
  width: 100%;
}

.channel-config-table {
  background: rgba(30, 41, 59, 0.6);
  border-radius: 6px;
  overflow: hidden;
}

.channel-config-table .el-table__header {
  background: rgba(51, 65, 85, 0.8);
}

.channel-config-table .el-table__header th {
  background: rgba(51, 65, 85, 0.8);
  color: #f8fafc;
  font-weight: 600;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
}

.channel-config-table .el-table__body tr {
  background: rgba(30, 41, 59, 0.6);
}

.channel-config-table .el-table__body tr:hover {
  background: rgba(51, 65, 85, 0.6);
}

.channel-config-table .el-table__body td {
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
  padding: 8px;
}

.channel-config-table .el-input__inner,
.channel-config-table .el-select .el-input__inner {
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(147, 197, 253, 0.2);
  color: #f8fafc;
}

.channel-config-table .el-input__inner:focus,
.channel-config-table .el-select .el-input__inner:focus {
  border-color: rgba(96, 165, 250, 0.4);
}

.no-channels {
  text-align: center;
  padding: 20px;
  color: #94a3b8;
}
</style>
