export interface MaintainLogVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 车辆ID
   */
  vehicleId: string | number;

  /**
   * 车牌号
   */
  plateNumber: string;

  /**
   * 车辆编号
   */
  vehicleNumber: string;

  /**
   * 维修日期
   */
  maintenanceDate: string;

  /**
   * 维修类型(maintenance:保养, repair:维修, inspection:年检)
   */
  maintenanceType: string;

  /**
   * 维修项目
   */
  maintenanceItem: string;

  /**
   * 维修内容详情
   */
  maintenanceContent: string;

  /**
   * 费用(元)
   */
  cost: string;

  /**
   * 维修厂
   */
  repairShop: string;

  /**
   * 里程数(km)
   */
  mileage: string;

  /**
   * 状态(pending:待处理, in_progress:进行中, completed:已完成)
   */
  status: string;

  /**
   * 备注信息
   */
  remark: string;

  /**
   * 组织机构id
   */
  deptId: string | number;

}

export interface MaintainLogForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 车辆ID
   */
  vehicleId?: string | number;

  /**
   * 车牌号
   */
  plateNumber?: string;

  /**
   * 车辆编号
   */
  vehicleNumber?: string;

  /**
   * 维修日期
   */
  maintenanceDate?: string;

  /**
   * 维修类型(maintenance:保养, repair:维修, inspection:年检)
   */
  maintenanceType?: string;

  /**
   * 维修项目
   */
  maintenanceItem?: string;

  /**
   * 维修内容详情
   */
  maintenanceContent?: string;

  /**
   * 费用(元)
   */
  cost?: string;

  /**
   * 维修厂
   */
  repairShop?: string;

  /**
   * 里程数(km)
   */
  mileage?: string;

  /**
   * 状态(pending:待处理, in_progress:进行中, completed:已完成)
   */
  status?: string;

  /**
   * 备注信息
   */
  remark?: string;

  /**
   * 组织机构id
   */
  deptId?: string | number;

}

export interface MaintainLogQuery extends PageQuery {

  /**
   * 车辆ID
   */
  vehicleId?: string | number;

  /**
   * 车牌号
   */
  plateNumber?: string;

  /**
   * 车辆编号
   */
  vehicleNumber?: string;

  /**
   * 维修日期
   */
  maintenanceDate?: string;

  /**
   * 维修类型(maintenance:保养, repair:维修, inspection:年检)
   */
  maintenanceType?: string;

  /**
   * 维修项目
   */
  maintenanceItem?: string;

  /**
   * 维修内容详情
   */
  maintenanceContent?: string;

  /**
   * 费用(元)
   */
  cost?: string;

  /**
   * 维修厂
   */
  repairShop?: string;

  /**
   * 里程数(km)
   */
  mileage?: string;

  /**
   * 状态(pending:待处理, in_progress:进行中, completed:已完成)
   */
  status?: string;

  /**
   * 组织机构id
   */
  deptId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}



