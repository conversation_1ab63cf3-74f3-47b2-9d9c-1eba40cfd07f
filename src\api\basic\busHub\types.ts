export interface BusHubVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 场站名称
   */
  hubName: string;

  /**
   * 场站编号
   */
  hubCode: string;

  /**
   * 场站类型(terminal:公交首末站,dispatch:调   度中心,parking:停车场,maintenance:维修站)
   */
  hubType: string;

  /**
   * 详细地址
   */
  address: string;

  /**
   * 经度
   */
  longitude: string;

  /**
   * 纬度
   */
  latitude: string;

  /**
   * 车位数量
   */
  parkingSpaces: number;

  /**
   * 已停车辆数
   */
  occupiedSpaces: string;

  /**
   * 管理员姓名
   */
  manager: string;

  /**
   * 联系电话
   */
  phone: string;

  /**
   * 状态(0:停用,1:运营中,2:维护中)
   */
  status: string;

  /**
   * 场站范围多边形坐标数据(JSON格式)
   */
  polygonData: string;

  /**
   * 场站面积(平方米)
   */
  polygonArea: string;

  /**
   * 场站周长(米)
   */
  polygonPerimeter: string;

  /**
   * 备注信息
   */
  remark: string;

  /**
   * 组织机构id
   */
  deptId: string | number;

}

export interface BusHubForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 场站名称
   */
  hubName?: string;

  /**
   * 场站编号
   */
  hubCode?: string;

  /**
   * 场站类型(terminal:公交首末站,dispatch:调   度中心,parking:停车场,maintenance:维修站)
   */
  hubType?: string;

  /**
   * 详细地址
   */
  address?: string;

  /**
   * 经度
   */
  longitude?: string;

  /**
   * 纬度
   */
  latitude?: string;

  /**
   * 车位数量
   */
  parkingSpaces?: number;

  /**
   * 已停车辆数
   */
  occupiedSpaces?: string;

  /**
   * 管理员姓名
   */
  manager?: string;

  /**
   * 联系电话
   */
  phone?: string;

  /**
   * 状态(0:停用,1:运营中,2:维护中)
   */
  status?: string;

  /**
   * 场站范围多边形坐标数据(JSON格式)
   */
  polygonData?: string;

  /**
   * 场站面积(平方米)
   */
  polygonArea?: string;

  /**
   * 场站周长(米)
   */
  polygonPerimeter?: string;

  /**
   * 备注信息
   */
  remark?: string;

  /**
   * 组织机构id
   */
  deptId?: string | number;

  /**
   * 前端使用的坐标数组 [经度, 纬度]
   */
  coordinates?: number[];

  /**
   * 前端使用的多边形坐标数组
   */
  polygon?: number[][];

}

export interface BusHubQuery extends PageQuery {

  /**
   * 场站名称
   */
  hubName?: string;

  /**
   * 场站编号
   */
  hubCode?: string;

  /**
   * 场站类型(terminal:公交首末站,dispatch:调   度中心,parking:停车场,maintenance:维修站)
   */
  hubType?: string;

  /**
   * 详细地址
   */
  address?: string;

  /**
   * 经度
   */
  longitude?: string;

  /**
   * 纬度
   */
  latitude?: string;

  /**
   * 车位数量
   */
  parkingSpaces?: number;

  /**
   * 已停车辆数
   */
  occupiedSpaces?: string;

  /**
   * 管理员姓名
   */
  manager?: string;

  /**
   * 联系电话
   */
  phone?: string;

  /**
   * 状态(0:停用,1:运营中,2:维护中)
   */
  status?: string;

  /**
   * 场站范围多边形坐标数据(JSON格式)
   */
  polygonData?: string;

  /**
   * 场站面积(平方米)
   */
  polygonArea?: string;

  /**
   * 场站周长(米)
   */
  polygonPerimeter?: string;

  /**
   * 组织机构id
   */
  deptId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}



