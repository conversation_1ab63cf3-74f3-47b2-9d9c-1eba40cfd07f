<template>
  <div class='driver-mileage-analysis'>
    <el-row :gutter='20'>
      <!-- 左侧组织机构驾驶员树 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <User />
              </el-icon>
              <span>组织机构筛选</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入组织机构' prefix-icon='Search' clearable />
            </div>

            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            >
              <template #default='{ node, data }'>
                <span class='tree-node'>
                  <el-icon class='dept-icon' :class="getNodeIconClass(data.type)">
                    <OfficeBuilding v-if="data.type === 'dept'" />
                    <User v-else-if="data.type === 'driver'" />
                  </el-icon>
                  <span class='node-label' :class="data.type">{{ data.label }}</span>
                  <span v-if="data.type === 'driver'" class='driver-work-number'>({{ data.workNumber }})</span>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>

      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 筛选条件区域 -->
          <div class='filter-section'>
            <el-card class='filter-card'>
              <!-- 时间分析选择器 -->
              <TimeAnalysisSelector
                :initialAnalysisType="queryParams.analysisType"
                @params-change="handleTimeAnalysisChange"
                ref="timeAnalysisSelectorRef"
              >
                <template #actions>
                  <el-form-item>
                    <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
                    <el-button @click="resetQuery" icon="Refresh">重置</el-button>
                  </el-form-item>
                </template>
              </TimeAnalysisSelector>
            </el-card>
          </div>

          <!-- Tab切换区域 -->
          <div class='tabs-section'>
            <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
              <!-- 图表视图 -->
              <el-tab-pane label="图表分析" name="charts">
                <div class="tab-content">
                  <!-- 提示信息区域 -->
                  <div v-if="!hasValidQuery" class="query-tips">
                    <el-alert
                      title="使用提示"
                      type="info"
                      :closable="false"
                      show-icon
                    >
                      <template #default>
                        <div class="tips-content">
                          <p>请按以下步骤进行数据分析：</p>
                          <ol>
                            <li>在左侧组织机构树中选择要分析的<strong>组织机构或司机</strong></li>
                            <li>选择<strong>分析方式</strong>（年度/月度/日/小时分析）</li>
                            <li>选择<strong>时间范围</strong></li>
                            <li>点击<strong>查询</strong>按钮查看分析结果</li>
                          </ol>
                        </div>
                      </template>
                    </el-alert>
                  </div>

                  <!-- 统计卡片区域 -->
                  <div v-show="hasValidQuery" class='stats-section'>
                    <el-row :gutter='16'>
                      <el-col :span='6'>
                        <div class='stat-card total'>
                          <div class='stat-icon'>
                            <el-icon>
                              <TrendCharts />
                            </el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ statsData.totalMileage }}</div>
                            <div class='stat-label'>总里程(公里)</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card operational'>
                          <div class='stat-icon'>
                            <el-icon>
                              <DataAnalysis />
                            </el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ statsData.operationalMileage }}</div>
                            <div class='stat-label'>运营里程(公里)</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card non-operational'>
                          <div class='stat-icon'>
                            <el-icon>
                              <TopRight />
                            </el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ statsData.nonOperationalMileage }}</div>
                            <div class='stat-label'>非运营里程(公里)</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card drivers'>
                          <div class='stat-icon'>
                            <el-icon>
                              <User />
                            </el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ statsData.driverCount }}</div>
                            <div class='stat-label'>驾驶员数量</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 图表区域 -->
                  <div class='charts-section'>
                    <el-row :gutter='16'>
                      <el-col :span='24'>
                        <div class='chart-card'>
                          <div class='chart-header'>
                            <h3>里程趋势图 - {{ getTrendTypeText(queryParams.analysisType) }}</h3>
                          </div>
                          <div ref='trendChartRef' class='chart-container'></div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 表格视图 -->
              <el-tab-pane label="明细表格" name="table">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <h4>驾驶员里程明细表</h4>
                      <div class="table-legend">
                        <div class="legend-item">
                          <span class="legend-color operational"></span>
                          <span class="legend-text">运营里程</span>
                        </div>
                        <div class="legend-item">
                          <span class="legend-color non-operational"></span>
                          <span class="legend-text">非运营里程</span>
                        </div>
                        <div class="legend-item">
                          <span class="legend-color total"></span>
                          <span class="legend-text">总里程</span>
                        </div>
                      </div>
                      <div class="table-toolbar">
                        <el-button type="success" size="small" icon="Download" @click="handleExportTable">导出表格</el-button>
                      </div>
                    </div>
                    <el-table :data="mileageTableData" @selection-change="handleSelectionChange" style="width: 100%" height="580" border>
                      <el-table-column type="selection" width="55" align="center" fixed="left" />
                      <el-table-column label="司机姓名" align="center" prop="driverName" width="100" fixed="left" />
                      <el-table-column label="工号" align="center" prop="workNumber" width="100" fixed="left" />
                      <el-table-column label="组织机构" align="center" prop="deptName" width="120" fixed="left" />
                      <el-table-column label="平均速度(km/h)" align="center" prop="averageSpeed" width="130" fixed="left" />
                      <el-table-column :label="getRunningTimeLabel()" align="center" prop="runningTime" width="120" fixed="left" />
                      <el-table-column label="车辆数" align="center" prop="vehicleCount" width="100" fixed="left">
                        <template #default="scope">
                          <el-button link type="primary" @click="handleVehicleDetail(scope.row)" size="small">
                            {{ scope.row.vehicleCount }}
                          </el-button>
                        </template>
                      </el-table-column>

                      <!-- 动态生成的日期列 -->
                      <el-table-column
                        v-for="dateCol in dynamicDateColumns"
                        :key="dateCol.prop"
                        :label="dateCol.label"
                        align="center"
                        :prop="dateCol.prop"
                      >
                        <template #default="scope">
                          <div class="mileage-cell">
                            <div class="operational-mileage">{{ scope.row[dateCol.operationalProp] || '-' }}</div>
                            <div class="non-operational-mileage">{{ scope.row[dateCol.nonOperationalProp] || '-' }}</div>
                            <div class="total-mileage">{{ (scope.row[dateCol.operationalProp] || 0) + (scope.row[dateCol.nonOperationalProp] || 0) || '-' }}</div>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="total > 0"
                      :total="total"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getList"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>

        </div>
      </el-col>
    </el-row>

    <!-- 详情弹窗 -->
    <el-dialog :title='`${detailData?.driverName} (${detailData?.workNumber}) - 驾驶里程详情`' v-model='showDetailDialog'
               width='800px' append-to-body>
      <div v-if='detailData' class='detail-content'>
        <el-descriptions :column='2' border>
          <el-descriptions-item label='司机姓名'>{{ detailData.driverName }}</el-descriptions-item>
          <el-descriptions-item label='工号'>{{ detailData.workNumber }}</el-descriptions-item>
          <el-descriptions-item label='手机号'>{{ detailData.phoneNumber }}</el-descriptions-item>
          <el-descriptions-item label='驾驶车辆'>{{ detailData.currentVehicle }}</el-descriptions-item>
          <el-descriptions-item label='所属线路'>{{ detailData.routeName }}</el-descriptions-item>
          <el-descriptions-item label='所属组织机构'>{{ detailData.deptName }}</el-descriptions-item>
          <el-descriptions-item label='工作班次'>{{ detailData.shift }}</el-descriptions-item>
          <el-descriptions-item label='统计日期'>{{ parseTime(detailData.statisticDate, '{y}-{m}-{d}') }}
          </el-descriptions-item>
          <el-descriptions-item label='当日驾驶里程'>{{ detailData.dailyMileage }}km</el-descriptions-item>
          <el-descriptions-item label='月度预估里程'>{{ detailData.monthlyMileage }}km</el-descriptions-item>
          <el-descriptions-item label='历史累计里程'>{{ detailData.totalMileage }}km</el-descriptions-item>
          <el-descriptions-item label='平均速度'>{{ detailData.averageSpeed }}km/h</el-descriptions-item>
          <el-descriptions-item label='驾驶时长'>{{ detailData.drivingTime }}小时</el-descriptions-item>
          <el-descriptions-item label='工作效率'>{{ detailData.workloadEfficiency }}km/h</el-descriptions-item>
          <el-descriptions-item label='违规次数'>{{ detailData.violationCount }}次</el-descriptions-item>
          <el-descriptions-item label='驾驶评分'>{{ detailData.drivingScore }}分</el-descriptions-item>
          <el-descriptions-item label='最大时速'>{{ detailData.maxSpeed }}km/h</el-descriptions-item>
          <el-descriptions-item label='驾龄'>{{ detailData.drivingExperience }}年</el-descriptions-item>
          <el-descriptions-item label='油耗'>{{ detailData.fuelConsumption }}L</el-descriptions-item>
          <el-descriptions-item label='里程状态'>
            <el-tag :type='getMileageStatusType(detailData.mileageStatus)' size='small'>
              {{ getMileageStatusText(detailData.mileageStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label='行驶路线' span='2'>{{ detailData.routeDetail || '暂无详细路线信息' }}</el-descriptions-item>
          <el-descriptions-item label='违规详情' span='2'>{{ detailData.violationDetail || '无违规记录' }}</el-descriptions-item>
          <el-descriptions-item label='工作备注' span='2'>{{ detailData.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class='dialog-footer'>
          <el-button @click='showDetailDialog = false'>关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 车辆详情弹窗 -->
    <el-dialog :title="`${vehicleDetailData?.driverName} - 车辆列表`" v-model="showVehicleDetailDialog" width="600px" append-to-body>
      <div v-if="vehicleDetailData" class="vehicle-detail-content">
        <el-table :data="vehicleDetailData.vehicles" border>
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column label="车牌号" prop="plateNumber" align="center" />
          <el-table-column label="车辆编号" prop="vehicleNumber" align="center" />
          <el-table-column label="所属线路" prop="routeName" align="center" />
          <el-table-column label="状态" prop="status" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status === '运营中' ? 'success' : 'info'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showVehicleDetailDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name='DriverMileageAnalysis'>
import { ref, reactive, onMounted, nextTick, watchEffect } from 'vue';
import { TrendCharts, DataAnalysis, TopRight, User, OfficeBuilding } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import TimeAnalysisSelector from '@/components/TimeAnalysisSelector/index.vue';

const loading = ref(false);
const showDetailDialog = ref(false);
const detailData = ref(null);

const showVehicleDetailDialog = ref(false);
const vehicleDetailData = ref(null);

// Tab相关数据
const activeTab = ref('charts');
const mileageTableData = ref([]);
const total = ref(0);
const dynamicDateColumns = ref([]);
const selectedRecords = ref([]);

// 时间分析选择器引用
const timeAnalysisSelectorRef = ref();

// 树形相关
const deptName = ref('');
const deptTreeRef = ref();

const trendChartRef = ref(null);
let trendChart = null;

const queryRef = ref();

const statsData = ref({
  totalMileage: '89,652',
  operationalMileage: '62,756',
  nonOperationalMileage: '26,896',
  driverCount: '368'
});

// 移除线路选项，不再需要

// 组织机构树数据
const deptOptions = ref([]);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  dateRange: null,
  deptId: null,
  driverId: null,
  // 时间分析参数
  analysisType: '3', // 默认日分析
  startTime: null,
  endTime: null
});

onMounted(() => {
  getTreeSelect();
  generateDynamicDateColumns();
  nextTick(() => {
    initCharts();
  });
});

/** 组织机构树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** 查询组织机构下拉树结构，包含司机信息 */
async function getTreeSelect() {
  try {
    // 模拟组织机构数据，包含司机节点
    deptOptions.value = [
      {
        id: 1,
        label: '总公司',
        type: 'dept',
        children: [
          {
            id: 2,
            label: '运营部',
            type: 'dept',
            children: [
              {
                id: 3,
                label: '第一车队',
                type: 'dept',
                children: [
                  {
                    id: 301,
                    label: '张志明',
                    workNumber: 'D001',
                    type: 'driver',
                    deptId: 3
                  },
                  {
                    id: 302,
                    label: '陈美丽',
                    workNumber: 'D004',
                    type: 'driver',
                    deptId: 3
                  },
                  {
                    id: 303,
                    label: '孙悟空',
                    workNumber: 'D007',
                    type: 'driver',
                    deptId: 3
                  },
                  {
                    id: 304,
                    label: '郑志明',
                    workNumber: 'D010',
                    type: 'driver',
                    deptId: 3
                  },
                  {
                    id: 305,
                    label: '李四',
                    workNumber: 'D014',
                    type: 'driver',
                    deptId: 3
                  }
                ]
              },
              {
                id: 4,
                label: '第二车队',
                type: 'dept',
                children: [
                  {
                    id: 401,
                    label: '李华强',
                    workNumber: 'D002',
                    type: 'driver',
                    deptId: 4
                  },
                  {
                    id: 402,
                    label: '刘德华',
                    workNumber: 'D005',
                    type: 'driver',
                    deptId: 4
                  },
                  {
                    id: 403,
                    label: '朱小明',
                    workNumber: 'D008',
                    type: 'driver',
                    deptId: 4
                  },
                  {
                    id: 404,
                    label: '王东',
                    workNumber: 'D012',
                    type: 'driver',
                    deptId: 4
                  }
                ]
              },
              {
                id: 5,
                label: '第三车队',
                type: 'dept',
                children: [
                  {
                    id: 501,
                    label: '赵雅芝',
                    workNumber: 'D006',
                    type: 'driver',
                    deptId: 5
                  },
                  {
                    id: 502,
                    label: '吴亚辉',
                    workNumber: 'D009',
                    type: 'driver',
                    deptId: 5
                  },
                  {
                    id: 503,
                    label: '张三',
                    workNumber: 'D013',
                    type: 'driver',
                    deptId: 5
                  }
                ]
              }
            ]
          },
          {
            id: 6,
            label: '维修部',
            type: 'dept',
            children: [
              {
                id: 7,
                label: '机修组',
                type: 'dept',
                children: [
                  {
                    id: 701,
                    label: '王建国',
                    workNumber: 'D003',
                    type: 'driver',
                    deptId: 7
                  },
                  {
                    id: 702,
                    label: '王五',
                    workNumber: 'D015',
                    type: 'driver',
                    deptId: 7
                  }
                ]
              },
              {
                id: 8,
                label: '电修组',
                type: 'dept',
                children: [
                  {
                    id: 801,
                    label: '李直',
                    workNumber: 'D011',
                    type: 'driver',
                    deptId: 8
                  }
                ]
              }
            ]
          }
        ]
      }
    ];
  } catch (error) {
    console.error('获取组织机构树失败:', error);
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  if (data.type === 'dept') {
    // 点击组织机构节点，按组织机构筛选
    queryParams.deptId = data.id;
    queryParams.driverId = null;
  } else if (data.type === 'driver') {
    // 点击司机节点，按司机筛选
    queryParams.driverId = data.id;
    queryParams.deptId = data.deptId; // 保留所属部门信息
  }
  handleQuery();
}

function refreshData() {
  // 仅更新图表数据
  updateChartsWithFilteredData();
}

function initCharts() {
  initTrendChart();
}

function initTrendChart() {
  if (!trendChartRef.value) return;

  trendChart = echarts.init(trendChartRef.value);

  // 根据当前分析类型生成初始数据
  const chartData = generateFilteredChartData();
  const xAxisData = generateXAxisData();

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' }
    },
    legend: {
      data: ['运营里程', '非运营里程'],
      textStyle: { color: '#e5e7eb' },
      top: '5%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: {
        color: '#94a3b8'
      },
      axisLine: {
        lineStyle: {
          color: '#374151'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '里程(km)',
      nameTextStyle: { color: '#94a3b8' },
      axisLabel: { color: '#94a3b8' },
      axisLine: { lineStyle: { color: '#374151' } },
      splitLine: { lineStyle: { color: '#374151', type: 'dashed' } }
    },
    series: [
      {
        name: '运营里程',
        type: 'bar',
        stack: 'total',
        data: chartData.operationalMileage,
        itemStyle: { color: '#67C23A' },
        barWidth: '50%'
      },
      {
        name: '非运营里程',
        type: 'bar',
        stack: 'total',
        data: chartData.nonOperationalMileage,
        itemStyle: { color: '#E6A23C' },
        barWidth: '50%'
      }
    ]
  };
  trendChart.setOption(option);
}

function handleQuery() {
  // 验证必填字段
  if (!validateRequiredFields()) {
    return;
  }

  // 查询时更新图表
  updateChartsWithFilteredData();
}

function resetQuery() {
  queryRef.value?.resetFields();
  queryParams.deptId = null;
  queryParams.driverId = null;
  queryParams.analysisType = '3'; // 重置为日分析
  queryParams.startTime = null;
  queryParams.endTime = null;
  deptTreeRef.value?.setCurrentKey(null);

  // 重置时间分析选择器
  timeAnalysisSelectorRef.value?.reset();

  // 重置时恢复默认图表数据
  initTrendChart();
}


function handleDetail(row) {
  detailData.value = row;
  showDetailDialog.value = true;
}

function handleExport() {
  ElMessage.success('导出成功');
}

function handlePrint() {
  window.print();
}

function handleRefresh() {
  updateChartsWithFilteredData();
}

function updateChartsWithFilteredData() {
  if (!trendChart) return;

  // 生成基于筛选条件的模拟图表数据
  const chartData = generateFilteredChartData();

  // 根据分析类型和实际时间范围生成X轴数据
  const xAxisData = generateXAxisData();

  const option = {
    xAxis: {
      data: xAxisData
    },
    series: [
      {
        name: '运营里程',
        data: chartData.operationalMileage
      },
      {
        name: '非运营里程',
        data: chartData.nonOperationalMileage
      }
    ]
  };

  trendChart.setOption(option);
}

// 生成X轴数据
function generateXAxisData() {
  const analysisType = queryParams.analysisType;
  const startTime = queryParams.startTime;
  const endTime = queryParams.endTime;

  if (!startTime || !endTime) {
    // 如果没有时间范围，返回默认数据
    return getDefaultXAxisData(analysisType);
  }

  const startDate = new Date(startTime);
  const endDate = new Date(endTime);
  const xAxisData = [];

  switch (analysisType) {
    case '0': // 年度分析
      const startYear = startDate.getFullYear();
      const endYear = endDate.getFullYear();
      for (let year = startYear; year <= endYear; year++) {
        xAxisData.push(year.toString());
      }
      break;

    case '1': // 周分析
      const weekStart = new Date(startDate);
      const weekEnd = new Date(endDate);
      let currentWeek = new Date(weekStart);
      let weekNum = 1;

      while (currentWeek <= weekEnd) {
        const weekLabel = `第${weekNum}周(${formatDate(currentWeek, 'MM-DD')})`;
        xAxisData.push(weekLabel);
        currentWeek.setDate(currentWeek.getDate() + 7);
        weekNum++;
      }
      break;

    case '2': // 月度分析
      const monthStart = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
      const monthEnd = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
      let currentMonth = new Date(monthStart);

      while (currentMonth <= monthEnd) {
        const monthLabel = `${currentMonth.getFullYear()}年${(currentMonth.getMonth() + 1).toString().padStart(2, '0')}月`;
        xAxisData.push(monthLabel);
        currentMonth.setMonth(currentMonth.getMonth() + 1);
      }
      break;

    case '3': // 日分析
      const dayStart = new Date(startDate);
      const dayEnd = new Date(endDate);
      let currentDay = new Date(dayStart);

      while (currentDay <= dayEnd) {
        const dayLabel = formatDate(currentDay, 'MM-DD');
        xAxisData.push(dayLabel);
        currentDay.setDate(currentDay.getDate() + 1);
      }
      break;

    case '4': // 小时分析
      const hourStart = new Date(startDate);
      const hourEnd = new Date(endDate);
      let currentHour = new Date(hourStart);

      while (currentHour <= hourEnd) {
        const hourLabel = formatDate(currentHour, 'MM-DD HH:00');
        xAxisData.push(hourLabel);
        currentHour.setHours(currentHour.getHours() + 1);
      }
      break;
  }

  return xAxisData.length > 0 ? xAxisData : getDefaultXAxisData(analysisType);
}

// 获取默认X轴数据
function getDefaultXAxisData(analysisType) {
  switch (analysisType) {
    case '0': // 年度
      return ['2023', '2024', '2025'];
    case '1': // 周
      return ['第1周', '第2周', '第3周', '第4周'];
    case '2': // 月度
      return ['2024年01月', '2024年02月', '2024年03月', '2024年04月', '2024年05月', '2024年06月'];
    case '3': // 日
      return ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    case '4': // 小时
      return ['08:00', '10:00', '12:00', '14:00', '16:00', '18:00'];
    default:
      return ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  }
}

// 格式化日期工具函数
function formatDate(date, format) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours);
}

// 生成基于筛选条件的图表数据
function generateFilteredChartData() {
  // 获取X轴数据确定数据点数量
  const xAxisData = generateXAxisData();
  const dataLength = xAxisData.length;

  // 根据分析类型生成不同的基础数据
  let baseOperational = [];
  let baseNonOperational = [];
  const analysisType = queryParams.analysisType;

  // 根据分析类型生成不同规模的基础数据
  let baseOperationalValue, baseNonOperationalValue;
  switch (analysisType) {
    case '0': // 年度
      baseOperationalValue = 40000; // 年运营里程基础值
      baseNonOperationalValue = 15000; // 年非运营里程基础值
      break;
    case '2': // 月度
      baseOperationalValue = 4000; // 月运营里程基础值
      baseNonOperationalValue = 1500; // 月非运营里程基础值
      break;
    case '3': // 日
      baseOperationalValue = 150; // 日运营里程基础值
      baseNonOperationalValue = 50; // 日非运营里程基础值
      break;
    case '4': // 小时
      baseOperationalValue = 12; // 时运营里程基础值
      baseNonOperationalValue = 5; // 时非运营里程基础值
      break;
    default:
      baseOperationalValue = 150;
      baseNonOperationalValue = 50;
  }

  // 生成对应数量的数据点
  for (let i = 0; i < dataLength; i++) {
    baseOperational.push(Math.floor(baseOperationalValue + Math.random() * baseOperationalValue * 0.4 - baseOperationalValue * 0.2));
    baseNonOperational.push(Math.floor(baseNonOperationalValue + Math.random() * baseNonOperationalValue * 0.4 - baseNonOperationalValue * 0.2));
  }

  // 根据不同筛选条件调整数据
  if (queryParams.driverId || queryParams.driverName) {
    // 单个驾驶员的数据通常更稳定但数量级较小
    baseOperational = baseOperational.map(val => Math.round(val * 0.8 + Math.random() * 50 - 25));
    baseNonOperational = baseNonOperational.map(val => Math.round(val * 0.6 + Math.random() * 20 - 10));
  } else if (queryParams.deptId) {
    // 组织机构数据相对稳定
    baseOperational = baseOperational.map(val => Math.round(val * 0.9 + Math.random() * 40 - 20));
    baseNonOperational = baseNonOperational.map(val => Math.round(val * 0.8 + Math.random() * 30 - 15));
  } else if (queryParams.routeId) {
    // 线路数据可能有特定模式
    const routeMultiplier = queryParams.routeId === '115' ? 1.1 :
                          queryParams.routeId === '301' ? 0.9 : 1.0;
    baseOperational = baseOperational.map(val => Math.round(val * routeMultiplier + Math.random() * 30 - 15));
    baseNonOperational = baseNonOperational.map(val => Math.round(val * routeMultiplier + Math.random() * 25 - 12));
  }

  return {
    operationalMileage: baseOperational,
    nonOperationalMileage: baseNonOperational
  };
}

// 查看车辆详情
function handleVehicleDetail(row) {
  vehicleDetailData.value = row;
  showVehicleDetailDialog.value = true;
}

// 处理时间分析参数变化
function handleTimeAnalysisChange(params) {
  queryParams.analysisType = params.analysisType;
  queryParams.startTime = params.startTime;
  queryParams.endTime = params.endTime;

  // 生成动态日期列
  generateDynamicDateColumns();

  // 自动查询数据（只有在有完整参数时才查询）
  if (params.startTime && params.endTime && (queryParams.deptId || queryParams.driverId)) {
    handleQuery();
  }
}

// 获取统计维度文本（基于分析类型）
function getTrendTypeText(analysisType) {
  const typeMap = {
    '0': '年度统计',
    '1': '周统计',
    '2': '月度统计',
    '3': '日统计',
    '4': '小时统计'
  };
  return typeMap[analysisType] || '日统计';
}

function getMileageStatusType(status) {
  const typeMap = {
    'normal': '',
    'high': 'success',
    'low': 'warning'
  };
  return typeMap[status] || 'info';
}

function getMileageStatusText(status) {
  const textMap = {
    'normal': '正常',
    'high': '偏高',
    'low': '偏低'
  };
  return textMap[status] || '未知';
}

function getDrivingScoreClass(score) {
  if (score >= 90) return 'score-excellent';
  if (score >= 80) return 'score-good';
  if (score >= 70) return 'score-average';
  return 'score-poor';
}

function getShiftType(shift) {
  const typeMap = {
    '早班': 'success',
    '晚班': 'warning',
    '全天班': ''
  };
  return typeMap[shift] || 'info';
}

function parseTime(time, pattern) {
  if (!time) return '';
  const date = new Date(time);
  const format = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours()
  };
  return pattern.replace(/{(y|m|d|h)+}/g, (result, key) => {
    const value = format[key];
    return key === 'y' ? value : value.toString().padStart(2, '0');
  });
}

// 获取节点图标样式类
function getNodeIconClass(type) {
  return type === 'driver' ? 'driver-icon' : 'dept-icon';
}

// 验证必填字段
function validateRequiredFields() {
  // 验证组织机构是否选择
  if (!queryParams.deptId && !queryParams.driverId) {
    ElMessage.warning('请先选择组织机构或司机');
    return false;
  }

  // 验证分析方式是否选择
  if (!queryParams.analysisType) {
    ElMessage.warning('请选择分析方式');
    return false;
  }

  // 验证时间范围是否选择
  if (!queryParams.startTime || !queryParams.endTime) {
    ElMessage.warning('请选择时间范围');
    return false;
  }

  return true;
}

// 生成动态日期列
function generateDynamicDateColumns() {
  const startTime = queryParams.startTime;
  const endTime = queryParams.endTime;
  const analysisType = queryParams.analysisType;

  if (!startTime || !endTime) {
    dynamicDateColumns.value = [];
    return;
  }

  const columns = [];
  const startDate = new Date(startTime);
  const endDate = new Date(endTime);

  switch (analysisType) {
    case '0': // 年度分析
      const startYear = startDate.getFullYear();
      const endYear = endDate.getFullYear();
      for (let year = startYear; year <= endYear; year++) {
        columns.push({
          label: `${year}年`,
          prop: `year_${year}`,
          operationalProp: `operational_year_${year}`,
          nonOperationalProp: `nonOperational_year_${year}`,
          date: year.toString()
        });
      }
      break;

    case '2': // 月度分析
      let currentMonth = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
      while (currentMonth <= endDate) {
        const monthLabel = `${currentMonth.getMonth() + 1}月`;
        const monthKey = `${currentMonth.getFullYear()}_${currentMonth.getMonth() + 1}`;
        columns.push({
          label: monthLabel,
          prop: `month_${monthKey}`,
          operationalProp: `operational_month_${monthKey}`,
          nonOperationalProp: `nonOperational_month_${monthKey}`,
          date: formatDate(currentMonth, 'YYYY-MM')
        });
        currentMonth.setMonth(currentMonth.getMonth() + 1);
      }
      break;

    case '3': // 日分析
      let currentDay = new Date(startDate);
      while (currentDay <= endDate) {
        const dayLabel = formatDate(currentDay, 'MM-DD');
        const dayKey = formatDate(currentDay, 'YYYY-MM-DD');
        columns.push({
          label: dayLabel,
          prop: `day_${dayKey}`,
          operationalProp: `operational_day_${dayKey}`,
          nonOperationalProp: `nonOperational_day_${dayKey}`,
          date: dayKey
        });
        currentDay.setDate(currentDay.getDate() + 1);
      }
      break;

    case '4': // 小时分析
      let currentHour = new Date(startDate);
      while (currentHour <= endDate) {
        const hourLabel = formatDate(currentHour, 'DD日HH时');
        const hourKey = formatDate(currentHour, 'YYYY-MM-DD-HH');
        columns.push({
          label: hourLabel,
          prop: `hour_${hourKey}`,
          operationalProp: `operational_hour_${hourKey}`,
          nonOperationalProp: `nonOperational_hour_${hourKey}`,
          date: formatDate(currentHour, 'YYYY-MM-DD HH:00')
        });
        currentHour.setHours(currentHour.getHours() + 1);
      }
      break;
  }

  dynamicDateColumns.value = columns;
}

// 获取运行时长标签
function getRunningTimeLabel() {
  const analysisType = queryParams.analysisType;
  const labelMap = {
    '0': '运行时长(年)',
    '2': '运行时长(月)',
    '3': '运行时长(天)',
    '4': '运行时长(h)'
  };
  return labelMap[analysisType] || '运行时长(天)';
}

// 获取运行时长单位
function getRunningTimeUnit() {
  const analysisType = queryParams.analysisType;
  const unitMap = {
    '0': '年',
    '2': '月',
    '3': '天',
    '4': '小时'
  };
  return unitMap[analysisType] || '天';
}

// Tab切换处理
function handleTabChange(tabName) {
  queryParams.pageNum = 1;
  if (tabName === 'table') {
    // 切换到表格视图时验证必填字段
    if (!validateRequiredFields()) {
      // 如果验证失败，切换回图表视图
      activeTab.value = 'charts';
      return;
    }
    getList();
  }
}

// 获取表格数据
function getList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateDriverMileageTableData();
    mileageTableData.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 获取时间周期标签
function getTimePeriodLabel() {
  const analysisType = queryParams.analysisType;
  const labelMap = {
    '0': '统计年份',
    '1': '统计周期',
    '2': '统计月份',
    '3': '统计日期',
    '4': '统计时间'
  };
  return labelMap[analysisType] || '统计日期';
}

// 获取里程标签
function getMileageLabel() {
  const analysisType = queryParams.analysisType;
  const labelMap = {
    '0': '年里程(km)',
    '1': '周里程(km)',
    '2': '月里程(km)',
    '3': '日里程(km)',
    '4': '时里程(km)'
  };
  return labelMap[analysisType] || '日里程(km)';
}

// 格式化统计日期
function formatStatisticDate(date) {
  if (!date) return '';
  const dateObj = new Date(date);
  const analysisType = queryParams.analysisType;

  switch (analysisType) {
    case '0': // 年度
      return dateObj.getFullYear().toString();
    case '1': // 周
      const weekNum = Math.ceil((dateObj.getDate()) / 7);
      return `${dateObj.getFullYear()}年第${weekNum}周`;
    case '2': // 月度
      return `${dateObj.getFullYear()}年${(dateObj.getMonth() + 1).toString().padStart(2, '0')}月`;
    case '3': // 日
      return parseTime(date, '{y}-{m}-{d}');
    case '4': // 小时
      return parseTime(date, '{y}-{m}-{d} {h}:00');
    default:
      return parseTime(date, '{y}-{m}-{d}');
  }
}

// 生成驾驶员里程表格模拟数据
function generateDriverMileageTableData() {
  const allDrivers = [
    { id: 301, driverName: '张志明', workNumber: 'D001', routeName: '115路', deptName: '第一车队', deptId: 3 },
    { id: 401, driverName: '李华强', workNumber: 'D002', routeName: '135路', deptName: '第二车队', deptId: 4 },
    { id: 701, driverName: '王建国', workNumber: 'D003', routeName: '201路', deptName: '机修组', deptId: 7 },
    { id: 302, driverName: '陈美丽', workNumber: 'D004', routeName: '202路', deptName: '第一车队', deptId: 3 },
    { id: 402, driverName: '刘德华', workNumber: 'D005', routeName: '301路', deptName: '第二车队', deptId: 4 },
    { id: 501, driverName: '赵雅芝', workNumber: 'D006', routeName: '118路', deptName: '第三车队', deptId: 5 },
    { id: 303, driverName: '孙悟空', workNumber: 'D007', routeName: '203路', deptName: '第一车队', deptId: 3 },
    { id: 403, driverName: '朱小明', workNumber: 'D008', routeName: '305路', deptName: '第二车队', deptId: 4 },
    { id: 502, driverName: '吴亚辉', workNumber: 'D009', routeName: '120路', deptName: '第三车队', deptId: 5 },
    { id: 304, driverName: '郑志明', workNumber: 'D010', routeName: '138路', deptName: '第一车队', deptId: 3 },
    { id: 801, driverName: '李直', workNumber: 'D011', routeName: '205路', deptName: '电修组', deptId: 8 },
    { id: 404, driverName: '王东', workNumber: 'D012', routeName: '310路', deptName: '第二车队', deptId: 4 },
    { id: 503, driverName: '张三', workNumber: 'D013', routeName: '125路', deptName: '第三车队', deptId: 5 },
    { id: 305, driverName: '李四', workNumber: 'D014', routeName: '140路', deptName: '第一车队', deptId: 3 },
    { id: 702, driverName: '王五', workNumber: 'D015', routeName: '208路', deptName: '机修组', deptId: 7 }
  ];

  // 根据查询条件过滤司机数据
  let filteredDrivers = allDrivers;

  if (queryParams.driverId) {
    // 如果选择了特定司机，只显示该司机
    filteredDrivers = allDrivers.filter(driver => driver.id === queryParams.driverId);
  } else if (queryParams.deptId) {
    // 如果选择了部门，显示该部门下的所有司机
    const deptDriverMap = {
      3: [301, 302, 303, 304, 305], // 第一车队
      4: [401, 402, 403, 404], // 第二车队
      5: [501, 502, 503], // 第三车队
      7: [701, 702], // 机修组
      8: [801], // 电修组
      2: [301, 302, 303, 304, 305, 401, 402, 403, 404, 501, 502, 503], // 运营部（包含所有车队）
      6: [701, 702, 801], // 维修部（包含机修组和电修组）
      1: allDrivers.map(d => d.id) // 总公司（包含所有司机）
    };

    const driverIds = deptDriverMap[queryParams.deptId] || [];
    filteredDrivers = allDrivers.filter(driver => driverIds.includes(driver.id));
  }

  const data = [];

  // 为每个过滤后的驾驶员生成一行数据
  filteredDrivers.forEach((driver, index) => {
    const vehicleCount = Math.floor(Math.random() * 3) + 2; // 2-4辆车辆

    // 生成车辆信息
    const vehicles = [];
    const plateNumbers = ['京A12345', '京A12346', '京A12347', '京A12348', '京A12349', '京A12350'];
    const statuses = ['运营中', '停运'];

    for (let i = 0; i < vehicleCount; i++) {
      vehicles.push({
        plateNumber: plateNumbers[Math.floor(Math.random() * plateNumbers.length)],
        vehicleNumber: `V${String(driver.id * 10 + i + 1).padStart(3, '0')}`,
        routeName: driver.routeName,
        status: statuses[Math.floor(Math.random() * statuses.length)]
      });
    }

    const rowData = {
      id: driver.id,
      ...driver,
      vehicleCount,
      vehicles,
      averageSpeed: Math.floor(Math.random() * 20) + 35,
      runningTime: 1
    };

    // 为每个动态日期列生成数据
    dynamicDateColumns.value.forEach(dateCol => {
      // 生成运营里程和非运营里程
      const operationalMileage = Math.floor(Math.random() * 200) + 100;
      const nonOperationalMileage = Math.floor(Math.random() * 80) + 20;

      rowData[dateCol.operationalProp] = operationalMileage;
      rowData[dateCol.nonOperationalProp] = nonOperationalMileage;
    });

    data.push(rowData);
  });

  return data;

}

// 表格选择变化
function handleSelectionChange(selection) {
  selectedRecords.value = selection;
}

// 导出表格数据
function handleExportTable() {
  ElMessage.success('表格数据导出成功');
}

// 打印表格
function handlePrintTable() {
  window.print();
}
</script>

<style scoped>
.driver-mileage-analysis {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 筛选条件区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  background: rgba(15, 23, 42, 0.98) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.filter-section {
  color: #e5e7eb;
}

/* 右侧内容区域 */
.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(41, 52, 70, 0.6) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.4);
}

.stat-card.total {
  border-left: 4px solid #409EFF;
}

.stat-card.operational {
  border-left: 4px solid #67C23A;
}

.stat-card.non-operational {
  border-left: 4px solid #E6A23C;
}

.stat-card.drivers {
  border-left: 4px solid #F56C6C;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #60a5fa;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #94a3b8;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  height: 400px;
}

.chart-card .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.chart-card .chart-header h3 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.chart-container {
  height: 320px;
  width: 100%;
}


/* 里程单元格样式 */
.mileage-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
}

.mileage-cell .operational-mileage {
  color: #67C23A;
  font-weight: 600;
}

.mileage-cell .non-operational-mileage {
  color: #E6A23C;
  font-weight: 600;
}

.mileage-cell .total-mileage {
  color: #409EFF;
  font-weight: 700;
  border-top: 1px solid rgba(147, 197, 253, 0.2);
  padding-top: 2px;
  margin-top: 2px;
}

/* 表格图例样式 */
.table-legend {
  display: flex;
  gap: 20px;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.operational {
  background-color: #67C23A;
}

.legend-color.non-operational {
  background-color: #E6A23C;
}

.legend-color.total {
  background-color: #409EFF;
}

.legend-text {
  font-size: 12px;
  color: #94a3b8;
}
.score-excellent {
  color: #67C23A;
  font-weight: 600;
}

.score-good {
  color: #409EFF;
  font-weight: 600;
}

.score-average {
  color: #E6A23C;
  font-weight: 600;
}

.score-poor {
  color: #F56C6C;
  font-weight: 600;
}

/* 详情弹窗 */
.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

/* Element UI组件覆盖 */
:deep(.el-form-item__label) {
  color: #e5e7eb !important;
}

:deep(.el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e5e7eb !important;
}

:deep(.el-input__inner) {
  color: #e5e7eb !important;
}

:deep(.el-input__inner::placeholder) {
  color: #64748b !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
}


/* 搜索框 */
.search-box {
  margin-bottom: 16px;
}

/* 树形组件样式 */
.dept-tree {
  background: transparent;
  color: #e5e7eb;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
}

.tree-node {
  display: flex;
  align-items: center;
  font-size: 14px;
  flex: 1;
}

.dept-icon {
  margin-right: 8px;
  color: #60a5fa;
  font-size: 16px;
  flex-shrink: 0;
}

.driver-icon {
  margin-right: 8px;
  color: #34d399;
  font-size: 14px;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  min-width: 0;
  white-space: nowrap;
}

.node-label.dept {
  font-weight: 500;
  color: #f1f5f9;
}

.node-label.driver {
  font-weight: 400;
  color: #d1fae5;
  font-size: 13px;
}

.driver-work-number {
  font-size: 11px;
  color: #94a3b8;
  margin-left: 4px;
  font-weight: 400;
}

/* Element Tree组件覆盖样式 */
:deep(.el-tree-node) {
  background: transparent;
}

:deep(.el-tree-node:hover > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 6px;
}

:deep(.el-tree-node__content) {
  background: transparent !important;
  border: none !important;
  color: #e5e7eb !important;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

:deep(.el-tree-node__content:hover) {
  background-color: rgba(59, 130, 246, 0.15) !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
}

:deep(.el-tree-node__expand-icon) {
  color: #64748b !important;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree .el-tree-node__children) {
  overflow: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .driver-mileage-analysis {
    padding: 10px;
  }

  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .chart-card {
    height: 300px;
  }

  .chart-container {
    height: 220px;
  }

  .table-container {
    min-height: 400px;
    padding: 16px;
  }

  :deep(.el-table) {
    font-size: 12px;
  }

  .table-toolbar {
    flex-direction: column;
    gap: 8px;
  }
}

/* Tab区域样式 */
.tabs-section {
  margin-bottom: 24px;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.8);
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  background: transparent;
  border: none;
  color: #94a3b8;
  transition: all 0.3s ease;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item:hover) {
  background: rgba(59, 130, 246, 0.1);
  color: #93c5fd;
}

.tab-content {
  padding: 0;
}

/* 表格容器样式 */
.table-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 620px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.table-header h4 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.table-toolbar {
  display: flex;
  gap: 12px;
}

/* 表格样式增强 */
:deep(.el-table) {
  background: transparent !important;
  color: #e5e7eb !important;
}

:deep(.el-table tr) {
  background: rgba(15, 23, 42, 0.6) !important;
}

:deep(.el-table tr:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

:deep(.el-table th.el-table__cell) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-table td.el-table__cell) {
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e5e7eb !important;
}

:deep(.el-table__body-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: rgba(147, 197, 253, 0.3) transparent;
}

:deep(.el-table__body-wrapper)::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper)::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 3px;
}

:deep(.el-table__body-wrapper)::-webkit-scrollbar-thumb {
  background: rgba(147, 197, 253, 0.3);
  border-radius: 3px;
}

:deep(.el-table__body-wrapper)::-webkit-scrollbar-thumb:hover {
  background: rgba(147, 197, 253, 0.5);
}
</style>
