<template>
  <div class='arrival-ontime-analysis'>
    <el-row :gutter='20'>
      <!-- 左侧线路和站点树 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <Operation />
              </el-icon>
              <span>路线筛选</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入路线名称' prefix-icon='Search' clearable />
            </div>

            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            >
              <template #default="{ node, data }">
                <span class="tree-node">
                  <el-icon v-if="data.type === 'dept'" class="dept-icon">
                    <OfficeBuilding />
                  </el-icon>
                  <el-icon v-else-if="data.type === 'route'" class="route-icon">
                    <Connection />
                  </el-icon>
                  <span class="node-label" :class="data.type">{{ data.label }}</span>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>

      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 筛选条件区域 -->
          <div class='filter-section'>
            <el-card class='filter-card'>
              <!-- 时间分析选择器 -->
              <TimeAnalysisSelector
                :initialAnalysisType="queryParams.analysisType"
                :availableTypes="['0', '2', '3']"
                @params-change="handleTimeAnalysisChange"
                ref="timeAnalysisSelectorRef"
              >
                <template #actions>
                  <el-form-item label="准点状态" prop="onTimeStatus">
                    <el-select v-model="queryParams.onTimeStatus" placeholder="请选择状态" clearable style="width: 120px">
                      <el-option label="全部" value="" />
                      <el-option label="准点" value="ontime" />
                      <el-option label="早到" value="early" />
                      <el-option label="晚点" value="late" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
                    <el-button @click="resetQuery" icon="Refresh">重置</el-button>
                  </el-form-item>
                </template>
              </TimeAnalysisSelector>
            </el-card>
          </div>

          <!-- Tab切换区域 -->
          <div class='tabs-section'>
            <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
              <!-- 图表分析 -->
              <el-tab-pane label="图表分析" name="charts">
                <div class="tab-content">
                  <!-- 统计卡片区域 -->
                  <div class='stats-section'>
                    <el-row :gutter='16'>
                      <el-col :span='6'>
                        <div class='stat-card total'>
                          <div class='stat-icon'>
                            <el-icon><Connection /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.totalRoutes }}</div>
                            <div class='stat-label'>总线路数</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card highest'>
                          <div class='stat-icon'>
                            <el-icon><TopRight /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.bestOnTimeRate }}%</div>
                            <div class='stat-label'>最高准点率</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card average'>
                          <div class='stat-icon'>
                            <el-icon><TrendCharts /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.avgOnTimeRate }}%</div>
                            <div class='stat-label'>平均准点率</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card arrivals'>
                          <div class='stat-icon'>
                            <el-icon><Operation /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.totalArrivals }}</div>
                            <div class='stat-label'>总到站次数</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 图表区域 -->
                  <div class='charts-section'>
                    <el-row :gutter='16'>
                      <el-col :span='24'>
                        <div class='chart-card'>
                          <div class='chart-header'>
                            <h3>到站准点率趋势图 - {{ getAnalysisTypeText(queryParams.analysisType) }}</h3>
                          </div>
                          <div ref="onTimeChartRef" class='chart-container'></div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 明细表格 -->
              <el-tab-pane label="明细表格" name="table">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>到站准点率明细表</h4>
                        <span class="table-subtitle">点击数据格子查看详细信息</span>
                      </div>
                      <div class="table-actions">
                        <el-button type="success" size="small" icon="Download" @click="handleExport">导出表格</el-button>
                      </div>
                    </div>
                    <el-table :data="detailTableData" @selection-change="handleSelectionChange" style="width: 100%" height="580" border>
                      <el-table-column type="selection" width="55" align="center" fixed="left" />
                      <el-table-column label="路线" align="center" prop="name" width="140" fixed="left" />
                      <el-table-column label="类型" align="center" prop="type" width="80" fixed="left">
                        <template #default="scope">
                          <el-tag type="primary" size="small">
                            路线
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="总准点率" align="center" prop="totalOnTimeRate" width="120" fixed="left">
                        <template #default="scope">
                          <el-tag :type="getOnTimeRateType(scope.row.totalOnTimeRate)" size="small">
                            {{ scope.row.totalOnTimeRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="总到站数" align="center" prop="totalArrivals" width="120" fixed="left" />

                      <!-- 动态生成的日期列 -->
                      <el-table-column
                        v-for="dateCol in dynamicDateColumns"
                        :key="dateCol.prop"
                        :label="dateCol.label"
                        align="center"
                        :prop="dateCol.prop"
                      >
                        <template #default="scope">
                          <div class="ontime-cell" @click="handleOnTimeDetail(scope.row, dateCol)">
                            <div class="ontime-rate">{{ scope.row[dateCol.rateProp] || '-' }}%</div>
                            <div class="ontime-info">
                              <span class="ontime-trips">准点: {{ scope.row[dateCol.onTimeProp] || 0 }}</span>
                              <span class="late-trips">晚点: {{ scope.row[dateCol.lateProp] || 0 }}</span>
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="total > 0"
                      :total="total"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getList"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 每日统计弹窗（月/年维度使用） -->
    <el-dialog
      v-model="showDailyStatsDialog"
      :title="dailyStatsTitle"
      width="1000px"
      :destroy-on-close="true"
    >
      <div v-if="dailyStatsData" class="daily-stats-content">
        <el-table
          :data="dailyStatsData.dailyRecords"
          border
          size="small"
          max-height="500"
          style="width: 100%"
        >
          <el-table-column label="日期" prop="date" align="center" />
          <el-table-column label="星期" prop="dayOfWeek" align="center">
            <template #default="scope">
              <span class="day-of-week">星期{{ scope.row.dayOfWeek }}</span>
            </template>
          </el-table-column>
          <el-table-column label="计划到站" prop="plannedArrivals" align="center" />
          <el-table-column label="实际到站" prop="actualArrivals" align="center" />
          <el-table-column label="准点次数" prop="onTimeArrivals" align="center">
            <template #default="scope">
              <span class="ontime-arrivals">{{ scope.row.onTimeArrivals }}</span>
            </template>
          </el-table-column>
          <el-table-column label="晚点次数" prop="lateArrivals" align="center">
            <template #default="scope">
              <span class="late-arrivals">{{ scope.row.lateArrivals }}</span>
            </template>
          </el-table-column>
          <el-table-column label="准点率" prop="onTimeRate" align="center">
            <template #default="scope">
              <el-tag :type="getOnTimeRateType(scope.row.onTimeRate)" size="small">
                {{ scope.row.onTimeRate }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template #default="scope">
              <el-button type="primary" link size="small" @click="handleDailyStatsDetail(scope.row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDailyStatsDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 准点率详情弹窗 -->
    <el-dialog
      v-model="showOnTimeDetailDialog"
      :title="onTimeDetailTitle"
      width="1100px"
      :destroy-on-close="true"
    >
      <div v-if="onTimeDetailData" class="ontime-detail-content">
        <!-- 汇总信息 -->
        <div class="summary-info">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="summary-item">
                <span class="summary-label">计划到站</span>
                <span class="summary-value planned">{{ onTimeDetailData.plannedArrivals }}次</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <span class="summary-label">实际到站</span>
                <span class="summary-value actual">{{ onTimeDetailData.actualArrivals }}次</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <span class="summary-label">准点次数</span>
                <span class="summary-value ontime">{{ onTimeDetailData.onTimeArrivals }}次</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <span class="summary-label">准点率</span>
                <el-tag :type="getOnTimeRateType(onTimeDetailData.onTimeRate)" size="small">
                  {{ onTimeDetailData.onTimeRate }}%
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 到站明细列表 -->
        <div class="arrival-list">
          <h4>当日到站明细</h4>
          <el-table
            :data="onTimeDetailData.arrivalList"
            border
            size="small"
            max-height="400"
            style="width: 100%"
          >
            <el-table-column label="车牌号" prop="plateNumber" width="100" align="center" />
            <el-table-column label="上下行" prop="direction" width="80" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.direction === '上行' ? 'primary' : 'success'" size="small">
                  {{ scope.row.direction }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="起点站" prop="startStation" width="120" align="center" />
            <el-table-column label="终点站" prop="endStation" width="120" align="center" />
            <el-table-column label="计划到站" prop="scheduledTime" width="100" align="center" />
            <el-table-column label="实际到站" prop="actualTime" width="100" align="center" />
            <el-table-column label="时差" prop="timeDifference" width="80" align="center">
              <template #default="scope">
                <span :class="getTimeDifferenceClass(scope.row.timeDifference)">
                  {{ scope.row.timeDifference > 0 ? '+' : '' }}{{ scope.row.timeDifference }}分
                </span>
              </template>
            </el-table-column>
            <el-table-column label="状态" prop="status" width="80" align="center">
              <template #default="scope">
                <el-tag :type="getArrivalStatusType(scope.row.status)" size="small">
                  {{ getArrivalStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="驾驶员" prop="driverName" width="100" align="center" />
            <el-table-column label="备注" prop="remark" min-width="120" show-overflow-tooltip />
          </el-table>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showOnTimeDetailDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="dialogTitle" v-model="showDetailDialog" width="800px" append-to-body>
      <div v-if="detailData" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item v-if="detailData.routeName" label="线路名称">{{ detailData.routeName }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.routeNumber" label="线路编号">{{ detailData.routeNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.stationName" label="站点名称">{{ detailData.stationName }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.stationCode" label="站点编号">{{ detailData.stationCode }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.plateNumber" label="车牌号">{{ detailData.plateNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.vehicleNumber" label="车辆编号">{{ detailData.vehicleNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.driverName" label="驾驶员">{{ detailData.driverName }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.statisticDate" label="统计日期">{{ detailData.statisticDate }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.plannedArrivals" label="计划到站">{{ detailData.plannedArrivals }}次</el-descriptions-item>
          <el-descriptions-item v-if="detailData.actualArrivals" label="实际到站">{{ detailData.actualArrivals }}次</el-descriptions-item>
          <el-descriptions-item v-if="detailData.onTimeArrivals" label="准点到站">{{ detailData.onTimeArrivals }}次</el-descriptions-item>
          <el-descriptions-item v-if="detailData.onTimeRate" label="准点率">
            <el-tag :type="getOnTimeRateType(detailData.onTimeRate)" size="small">
              {{ detailData.onTimeRate }}%
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="detailData.avgDelay" label="平均延误">{{ detailData.avgDelay }}分钟</el-descriptions-item>
          <el-descriptions-item v-if="detailData.area" label="所属区域">{{ detailData.area }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.routeCount" label="途径线路数">{{ detailData.routeCount }}条</el-descriptions-item>
          <el-descriptions-item v-if="detailData.earlyArrivals" label="早到次数">{{ detailData.earlyArrivals }}次</el-descriptions-item>
          <el-descriptions-item v-if="detailData.lateArrivals" label="晚点次数">{{ detailData.lateArrivals }}次</el-descriptions-item>
          <el-descriptions-item label="备注" span="2">{{ detailData.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ArrivalOnTimeAnalysis">
import { ref, reactive, onMounted, nextTick, watchEffect, computed } from 'vue';
import { Location, Connection, TrendCharts, Operation, OfficeBuilding, TopRight } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import TimeAnalysisSelector from '@/components/TimeAnalysisSelector/index.vue';

const loading = ref(true);
const showDetailDialog = ref(false);
const detailData = ref(null);
const activeTab = ref('charts');

// 树形相关
const deptName = ref('');
const deptTreeRef = ref();

// 图表引用
const onTimeChartRef = ref(null);
let onTimeChart = null;

// 表单引用
const queryRef = ref();
const timeAnalysisSelectorRef = ref();

// 分页数据
const total = ref(0);

// 表格数据
const detailTableData = ref([]);
const dynamicDateColumns = ref([]);

// 统计数据
const summaryStats = ref({
  totalRoutes: '28',
  avgOnTimeRate: '89.3',
  bestOnTimeRate: '96.8',
  totalArrivals: '24,680'
});

// 准点率详情弹窗变量
const showOnTimeDetailDialog = ref(false);
const onTimeDetailData = ref(null);

// 准点率详情弹窗标题
const onTimeDetailTitle = computed(() => {
  if (!onTimeDetailData.value) return '到站准点率详情';
  return `${onTimeDetailData.value.name} - ${onTimeDetailData.value.date} 到站准点率详情`;
});

// 每日统计弹窗变量
const showDailyStatsDialog = ref(false);
const dailyStatsData = ref(null);
const dailyStatsTitle = ref('');

// 判断是否为月/年维度
const isMonthYearDimension = computed(() => {
  return queryParams.analysisType === '0' || queryParams.analysisType === '2'; // 年度或月度
});

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  analysisType: '3', // 默认日分析
  startTime: undefined,
  endTime: undefined,
  dateRange: null,
  onTimeStatus: null,
  routeId: null,
  deptId: null
});

// 计算弹窗标题
const dialogTitle = computed(() => {
  if (!detailData.value) return '详情';
  if (detailData.value.routeName && !detailData.value.plateNumber) {
    return `${detailData.value.routeName} - 线路到站准点率详情`;
  }
  if (detailData.value.plateNumber) {
    return `${detailData.value.plateNumber} - 车辆到站准点率详情`;
  }
  return '到站准点率详情';
});

// 线路和车辆树数据
const deptOptions = ref([]);

// 动态生成日期列
function generateDateColumns() {
  dynamicDateColumns.value = [];
  if (!queryParams.startTime || !queryParams.endTime) {
    return;
  }

  const start = new Date(queryParams.startTime);
  const end = new Date(queryParams.endTime);
  let index = 1;

  switch(queryParams.analysisType) {
    case '0': // 年度
      for (let year = start.getFullYear(); year <= end.getFullYear(); year++) {
        dynamicDateColumns.value.push({
          prop: `date${index}`,
          label: `${year}年`,
          rateProp: `rate_${index}`,
          onTimeProp: `ontime_${index}`,
          lateProp: `late_${index}`,
          totalProp: `total_${index}`
        });
        index++;
      }
      break;
    case '2': // 月度
      for (let d = new Date(start.getFullYear(), start.getMonth(), 1); d <= end; d.setMonth(d.getMonth() + 1)) {
        dynamicDateColumns.value.push({
          prop: `date${index}`,
          label: `${d.getMonth() + 1}月`,
          rateProp: `rate_${index}`,
          onTimeProp: `ontime_${index}`,
          lateProp: `late_${index}`,
          totalProp: `total_${index}`
        });
        index++;
      }
      break;
    case '3': // 日
      for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
        dynamicDateColumns.value.push({
          prop: `date${index}`,
          label: `${d.getMonth() + 1}/${d.getDate()}`,
          rateProp: `rate_${index}`,
          onTimeProp: `ontime_${index}`,
          lateProp: `late_${index}`,
          totalProp: `total_${index}`
        });
        index++;
      }
      break;
  }
}

onMounted(() => {
  getTreeSelect();
  handleTabChange(activeTab.value);
  nextTick(() => {
    initCharts();
  });
});

/** 组织机构树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** 查询组织机构和路线下拉树结构 */
async function getTreeSelect() {
  try {
    deptOptions.value = [
      {
        id: 1,
        label: '总公司',
        type: 'dept',
        children: [
          {
            id: 2,
            label: '运营部',
            type: 'dept',
            children: [
              { id: 11, label: '115路', type: 'route', routeNumber: '115', onTimeRate: 92.3, deptId: 2 },
              { id: 12, label: '135路', type: 'route', routeNumber: '135', onTimeRate: 88.7, deptId: 2 },
              { id: 13, label: '201路', type: 'route', routeNumber: '201', onTimeRate: 94.1, deptId: 2 }
            ]
          },
          {
            id: 3,
            label: '第二运营部',
            type: 'dept',
            children: [
              { id: 14, label: '301路', type: 'route', routeNumber: '301', onTimeRate: 89.5, deptId: 3 },
              { id: 15, label: '401路', type: 'route', routeNumber: '401', onTimeRate: 91.2, deptId: 3 }
            ]
          },
          {
            id: 4,
            label: '第三运营部',
            type: 'dept',
            children: [
              { id: 16, label: '501路', type: 'route', routeNumber: '501', onTimeRate: 87.8, deptId: 4 },
              { id: 17, label: '601路', type: 'route', routeNumber: '601', onTimeRate: 93.6, deptId: 4 }
            ]
          }
        ]
      }
    ];
  } catch (error) {
    console.error('获取组织机构路线树失败:', error);
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  if (data.type === 'route') {
    queryParams.routeId = data.id;
    queryParams.routeNumber = data.routeNumber;
    queryParams.deptId = data.deptId;
  } else if (data.type === 'dept') {
    queryParams.deptId = data.id;
    queryParams.routeId = null;
    queryParams.routeNumber = null;
  }
  handleQuery();
}

/** 时间分析参数变化处理 */
function handleTimeAnalysisChange(params) {
  queryParams.analysisType = params.analysisType;
  queryParams.startTime = params.startTime;
  queryParams.endTime = params.endTime;

  // 生成动态日期列
  generateDateColumns();

  // 更新统计数据和图表
  updateStatsData();
  if (activeTab.value === 'charts') {
    nextTick(() => {
      updateOnTimeChart();
    });
  }

  // 重新加载表格数据
  if (activeTab.value === 'table') {
    getList();
  }
}

/** 更新统计数据 */
function updateStatsData() {
  // 根据选中的时间范围和类型更新统计数据
  const analysisText = getAnalysisTypeText(queryParams.analysisType);
  const timeRange = getTimeRangeText();

  // 更新统计卡片数据
  summaryStats.value = {
    totalRoutes: '28',
    avgOnTimeRate: '89.3',
    bestOnTimeRate: '96.8',
    totalArrivals: '24,680'
  };
}

/** 获取分析类型文本 */
function getAnalysisTypeText(type) {
  const typeMap = {
    '0': '年度统计',
    '2': '月度统计',
    '3': '日统计'
  };
  return typeMap[type] || '日统计';
}

/** 获取时间范围文本 */
function getTimeRangeText() {
  if (!queryParams.startTime || !queryParams.endTime) {
    return '未选择时间范围';
  }

  const startTime = new Date(queryParams.startTime);
  const endTime = new Date(queryParams.endTime);

  switch(queryParams.analysisType) {
    case '0': // 年度
      return `${startTime.getFullYear()}年 - ${endTime.getFullYear()}年`;
    case '2': // 月度
      return `${startTime.getFullYear()}-${(startTime.getMonth() + 1).toString().padStart(2, '0')} 至 ${endTime.getFullYear()}-${(endTime.getMonth() + 1).toString().padStart(2, '0')}`;
    case '3': // 日
      return `${startTime.toISOString().split('T')[0]} 至 ${endTime.toISOString().split('T')[0]}`;
    default:
      return '未知时间范围';
  }
}

/** Tab切换事件 */
function handleTabChange(tabName) {
  queryParams.pageNum = 1;
  activeTab.value = tabName;
  loading.value = true;

  switch (tabName) {
    case 'charts':
      nextTick(() => {
        updateOnTimeChart();
      });
      loading.value = false;
      break;
    case 'table':
      generateDateColumns();
      getList();
      break;
  }
}

// 获取表格数据
function getList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateTableData();
    detailTableData.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 生成表格模拟数据
function generateTableData() {
  const routes = [
    { name: '115路', type: '路线', routeNumber: '115', totalOnTimeRate: '92.3', totalArrivals: '1,250' },
    { name: '135路', type: '路线', routeNumber: '135', totalOnTimeRate: '88.7', totalArrivals: '980' },
    { name: '201路', type: '路线', routeNumber: '201', totalOnTimeRate: '94.1', totalArrivals: '1,680' },
    { name: '301路', type: '路线', routeNumber: '301', totalOnTimeRate: '89.5', totalArrivals: '1,420' },
    { name: '401路', type: '路线', routeNumber: '401', totalOnTimeRate: '91.2', totalArrivals: '1,150' },
    { name: '501路', type: '路线', routeNumber: '501', totalOnTimeRate: '87.8', totalArrivals: '1,320' },
    { name: '601路', type: '路线', routeNumber: '601', totalOnTimeRate: '93.6', totalArrivals: '1,580' }
  ];

  return routes.map((item, index) => {
    const rowData = {
      id: index + 1,
      ...item
    };

    // 为每个动态列生成数据
    dynamicDateColumns.value.forEach((col, colIndex) => {
      const totalArrivals = Math.floor(Math.random() * 50 + 30);
      const onTimeArrivals = Math.floor(totalArrivals * (0.85 + Math.random() * 0.15));
      const lateArrivals = Math.floor((totalArrivals - onTimeArrivals) * (0.6 + Math.random() * 0.4));
      const rate = totalArrivals > 0 ? ((onTimeArrivals / totalArrivals) * 100).toFixed(1) : '0.0';

      rowData[col.rateProp] = rate;
      rowData[col.onTimeProp] = onTimeArrivals;
      rowData[col.lateProp] = lateArrivals;
      rowData[col.totalProp] = totalArrivals;
    });

    return rowData;
  });
}

// 初始化图表
function initCharts() {
  initOnTimeChart();
}

// 初始化到站准点率图表
function initOnTimeChart() {
  if (!onTimeChartRef.value) return;

  onTimeChart = echarts.init(onTimeChartRef.value);
  updateOnTimeChart();
}

// 更新到站准点率图表
function updateOnTimeChart() {
  if (!onTimeChart) return;

  const analysisType = queryParams.analysisType;
  let xAxisData = [];
  let onTimeData = [];
  let lateData = [];

  if (!queryParams.startTime || !queryParams.endTime) {
    xAxisData = ['1月', '2月', '3月', '4月', '5月', '6月'];
    onTimeData = [92.3, 88.7, 94.1, 89.5, 91.2, 93.6];
    lateData = [7.7, 11.3, 5.9, 10.5, 8.8, 6.4];
  } else {
    const start = new Date(queryParams.startTime);
    const end = new Date(queryParams.endTime);

    switch(analysisType) {
      case '0': // 年
        for (let year = start.getFullYear(); year <= end.getFullYear(); year++) {
          xAxisData.push(`${year}年`);
        }
        break;
      case '2': // 月
        for (let d = new Date(start.getFullYear(), start.getMonth(), 1); d <= end; d.setMonth(d.getMonth() + 1)) {
          xAxisData.push(`${d.getMonth() + 1}月`);
        }
        break;
      case '3': // 日
        for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
          xAxisData.push(`${d.getMonth() + 1}/${d.getDate()}`);
        }
        break;
      default:
        xAxisData = ['1月', '2月', '3月', '4月', '5月', '6月'];
    }

    onTimeData = generateSeriesData(xAxisData.length, 80, 98);
    lateData = onTimeData.map(rate => (100 - parseFloat(rate)).toFixed(1));
  }

  function generateSeriesData(length, min, max) {
    const data = [];
    for (let i = 0; i < length; i++) {
      data.push((Math.random() * (max - min) + min).toFixed(1));
    }
    return data;
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' }
    },
    legend: {
      data: ['准点率', '晚点率'],
      textStyle: { color: '#e5e7eb' },
      top: '5%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: { color: '#94a3b8' },
      axisLine: { lineStyle: { color: '#374151' } }
    },
    yAxis: {
      type: 'value',
      name: '百分比(%)',
      nameTextStyle: { color: '#94a3b8' },
      axisLabel: { color: '#94a3b8' },
      axisLine: { lineStyle: { color: '#374151' } },
      splitLine: { lineStyle: { color: '#374151' } }
    },
    series: [
      {
        name: '准点率',
        type: 'line',
        data: onTimeData,
        smooth: true,
        itemStyle: { color: '#67C23A' },
        lineStyle: { color: '#67C23A' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
            { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
          ])
        }
      },
      {
        name: '晚点率',
        type: 'line',
        data: lateData,
        smooth: true,
        itemStyle: { color: '#E6A23C' },
        lineStyle: { color: '#E6A23C' }
      }
    ]
  };

  onTimeChart.setOption(option);
}

// 查询
function handleQuery() {
  queryParams.pageNum = 1;
  // 更新统计数据
  updateStatsData();
  generateDateColumns();
  handleTabChange(activeTab.value);
}

// 重置
function resetQuery() {
  queryParams.onTimeStatus = null;
  queryParams.routeId = null;
  queryParams.deptId = null;
  timeAnalysisSelectorRef.value?.reset();
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

// 表格选择
function handleSelectionChange(selection) {
  // 处理表格选择
}

// 查看详情
function handleDetail(row) {
  detailData.value = row;
  showDetailDialog.value = true;
}

// 查看到站详情
function handleArrivalDetail(row) {
  detailData.value = {
    ...row,
    routeName: row.routeName,
    stationName: row.stationName
  };
  showDetailDialog.value = true;
}

// 处理准点率详情点击
function handleOnTimeDetail(row, dateCol) {
  if (isMonthYearDimension.value) {
    // 月/年维度: 显示每日统计数据表格
    const dailyStats = generateDailyStatsData(row, dateCol);
    dailyStatsData.value = dailyStats;
    dailyStatsTitle.value = `${row.name} - ${dateCol.label} 每日到站准点率统计`;
    showDailyStatsDialog.value = true;
  } else {
    // 日维度: 直接显示到站明细
    const detailData = generateOnTimeDetailData(row, dateCol);
    onTimeDetailData.value = detailData;
    showOnTimeDetailDialog.value = true;
  }
}

// 生成准点率详情模拟数据
function generateOnTimeDetailData(row, dateCol) {
  const plannedArrivals = Math.floor(Math.random() * 15 + 10); // 10-25次到站
  const actualArrivals = Math.floor(plannedArrivals * (0.95 + Math.random() * 0.05));
  const onTimeArrivals = Math.floor(actualArrivals * (0.80 + Math.random() * 0.20));
  const earlyArrivals = Math.floor(actualArrivals * (0.05 + Math.random() * 0.05));
  const lateArrivals = actualArrivals - onTimeArrivals - earlyArrivals;

  const onTimeRate = actualArrivals > 0 ? ((onTimeArrivals / actualArrivals) * 100).toFixed(1) : '0.0';

  // 生成到站明细列表
  const arrivalList = [];
  const drivers = ['张师傅', '李师傅', '王师傅', '陈师傅', '刘师傅', '赵师傅'];

  for (let i = 0; i < actualArrivals; i++) {
    const scheduledHour = 6 + Math.floor(i * 14 / actualArrivals); // 6:00-20:00分布
    const scheduledMinute = Math.floor(Math.random() * 60);
    const scheduledTime = `${scheduledHour.toString().padStart(2, '0')}:${scheduledMinute.toString().padStart(2, '0')}`;

    // 时差范围：-8到+15分钟
    const timeDifference = Math.round((Math.random() - 0.6) * 18);
    const actualMinute = scheduledMinute + timeDifference;
    const actualHour = scheduledHour + Math.floor(actualMinute / 60);
    const normalizedMinute = ((actualMinute % 60) + 60) % 60;
    const actualTime = `${actualHour.toString().padStart(2, '0')}:${normalizedMinute.toString().padStart(2, '0')}`;

    let status;
    if (timeDifference <= -3) status = 'early';
    else if (timeDifference >= 5) status = 'late';
    else status = 'ontime';

    // 随机生成上下行和站点信息
    const isUpDirection = Math.random() > 0.5;
    const direction = isUpDirection ? '上行' : '下行';
    const stations = {
      '上行': { start: '火车站', end: '汽车站' },
      '下行': { start: '汽车站', end: '火车站' }
    };

    arrivalList.push({
      plateNumber: `赣A${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${(1000 + Math.floor(Math.random() * 9000)).toString()}`,
      direction: direction,
      startStation: stations[direction].start,
      endStation: stations[direction].end,
      scheduledTime: scheduledTime,
      actualTime: actualTime,
      timeDifference: timeDifference,
      status: status,
      driverName: drivers[Math.floor(Math.random() * drivers.length)],
      remark: Math.random() > 0.7 ? (status === 'late' ? '交通拥堵' : status === 'early' ? '路况良好' : '正常运营') : ''
    });
  }

  return {
    name: row.name,
    type: row.type,
    date: dateCol.label,
    plannedArrivals: plannedArrivals,
    actualArrivals: actualArrivals,
    onTimeArrivals: onTimeArrivals,
    earlyArrivals: Math.max(0, earlyArrivals),
    lateArrivals: Math.max(0, lateArrivals),
    onTimeRate: onTimeRate,
    arrivalList: arrivalList
  };
}

// 生成每日统计数据（月/年维度点击格子后显示）
function generateDailyStatsData(row, dateCol) {
  const dailyRecords = [];

  // 根据分析维度确定日期范围
  let startDate, endDate;

  if (queryParams.analysisType === '0') {
    // 年度：生成该年份每天的数据
    const year = parseInt(dateCol.label.replace('年', ''));
    startDate = new Date(year, 0, 1);
    endDate = new Date(year, 11, 31);
  } else if (queryParams.analysisType === '2') {
    // 月度：生成该月每天的数据
    const month = parseInt(dateCol.label.replace('月', '')) - 1;
    const year = queryParams.startTime ? new Date(queryParams.startTime).getFullYear() : new Date().getFullYear();
    startDate = new Date(year, month, 1);
    endDate = new Date(year, month + 1, 0);
  }

  // 生成每日记录
  for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
    const plannedArrivals = Math.floor(Math.random() * 20 + 15);
    const actualArrivals = Math.floor(plannedArrivals * (0.95 + Math.random() * 0.05));
    const onTimeArrivals = Math.floor(actualArrivals * (0.85 + Math.random() * 0.15));
    const onTimeRate = actualArrivals > 0 ? ((onTimeArrivals / actualArrivals) * 100).toFixed(1) : '0.0';

    dailyRecords.push({
      id: `${d.getFullYear()}${(d.getMonth() + 1).toString().padStart(2, '0')}${d.getDate().toString().padStart(2, '0')}`,
      date: `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')}`,
      dayOfWeek: ['日', '一', '二', '三', '四', '五', '六'][d.getDay()],
      plannedArrivals: plannedArrivals,
      actualArrivals: actualArrivals,
      onTimeArrivals: onTimeArrivals,
      lateArrivals: actualArrivals - onTimeArrivals,
      onTimeRate: onTimeRate,
      routeName: row.name,
      routeNumber: row.routeNumber
    });
  }

  return {
    routeName: row.name,
    period: dateCol.label,
    dailyRecords: dailyRecords.sort((a, b) => new Date(b.date) - new Date(a.date))
  };
}

// 处理每日统计详情点击
function handleDailyStatsDetail(dailyRecord) {
  // 生成该日期的到站明细
  const detailData = generateDailyArrivalDetail(dailyRecord);
  onTimeDetailData.value = detailData;
  showOnTimeDetailDialog.value = true; // 打开到站明细弹窗
}

// 生成每日到站明细数据
function generateDailyArrivalDetail(record) {
  const arrivalList = [];
  const drivers = ['张师傅', '李师傅', '王师傅', '陈师傅', '刘师傅', '赵师傅'];

  for (let i = 0; i < record.actualArrivals; i++) {
    const scheduledHour = 6 + Math.floor(i * 14 / record.actualArrivals);
    const scheduledMinute = Math.floor(Math.random() * 60);
    const scheduledTime = `${scheduledHour.toString().padStart(2, '0')}:${scheduledMinute.toString().padStart(2, '0')}`;

    const timeDifference = Math.round((Math.random() - 0.6) * 18);
    const actualMinute = scheduledMinute + timeDifference;
    const actualHour = scheduledHour + Math.floor(actualMinute / 60);
    const normalizedMinute = ((actualMinute % 60) + 60) % 60;
    const actualTime = `${actualHour.toString().padStart(2, '0')}:${normalizedMinute.toString().padStart(2, '0')}`;

    let status;
    if (timeDifference <= -3) status = 'early';
    else if (timeDifference >= 5) status = 'late';
    else status = 'ontime';

    // 随机生成上下行和站点信息
    const isUpDirection = Math.random() > 0.5;
    const direction = isUpDirection ? '上行' : '下行';
    const stations = {
      '上行': { start: '火车站', end: '汽车站' },
      '下行': { start: '汽车站', end: '火车站' }
    };

    arrivalList.push({
      plateNumber: `赣A${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${(1000 + Math.floor(Math.random() * 9000)).toString()}`,
      direction: direction,
      startStation: stations[direction].start,
      endStation: stations[direction].end,
      scheduledTime: scheduledTime,
      actualTime: actualTime,
      timeDifference: timeDifference,
      status: status,
      driverName: drivers[Math.floor(Math.random() * drivers.length)],
      remark: Math.random() > 0.7 ? (status === 'late' ? '交通拥堵' : status === 'early' ? '路况良好' : '正常运营') : ''
    });
  }

  return {
    name: record.routeName,
    date: record.date,
    plannedArrivals: record.plannedArrivals,
    actualArrivals: record.actualArrivals,
    onTimeArrivals: record.onTimeArrivals,
    onTimeRate: record.onTimeRate,
    arrivalList: arrivalList.sort((a, b) => a.scheduledTime.localeCompare(b.scheduledTime))
  };
}

// 导出数据
function handleExport() {
  ElMessage.success('导出成功');
}

// 打印报表
function handlePrint() {
  window.print();
}

// 获取准点率类型
function getOnTimeRateType(rate) {
  const numRate = parseFloat(rate);
  if (numRate >= 95) return 'success';
  if (numRate >= 90) return 'info';
  if (numRate >= 80) return 'warning';
  return 'danger';
}

// 获取时差样式类
function getTimeDifferenceClass(timeDiff) {
  if (timeDiff <= -3) return 'early-time';
  if (timeDiff >= 5) return 'late-time';
  return 'ontime-time';
}

// 获取到站状态类型
function getArrivalStatusType(status) {
  const typeMap = {
    'ontime': 'success',
    'early': 'warning',
    'late': 'danger'
  };
  return typeMap[status] || 'info';
}

// 获取到站状态文本
function getArrivalStatusText(status) {
  const textMap = {
    'ontime': '准点',
    'early': '早到',
    'late': '晚点'
  };
  return textMap[status] || '未知';
}
</script>

<style scoped>
.arrival-ontime-analysis {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 左侧面板样式 */
.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.search-box {
  margin-bottom: 16px;
}

/* 右侧内容区域 */
.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 筛选条件区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  background: rgba(15, 23, 42, 0.98) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* Tab区域 */
.tabs-section {
  margin-bottom: 20px;
}

.tab-content {
  padding-top: 20px;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.time-range-info {
  margin-bottom: 16px;
}

.stat-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(41, 52, 70, 0.6) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.4);
}

.stat-card.total {
  border-left: 4px solid #409EFF;
}

.stat-card.stations {
  border-left: 4px solid #E6A23C;
}

.stat-card.average {
  border-left: 4px solid #67C23A;
}

.stat-card.arrivals {
  border-left: 4px solid #F56C6C;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #60a5fa;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #94a3b8;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  height: 400px;
}

.chart-card .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.chart-card .chart-header h3 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.chart-subtitle {
  font-size: 12px;
  color: #60a5fa;
  opacity: 0.8;
}

.chart-container {
  height: 320px;
  width: 100%;
}

/* 表格样式 */
.table-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 620px;
  height: calc(100vh - 480px);
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.table-header h4 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.table-subtitle {
  font-size: 12px;
  color: #60a5fa;
  opacity: 0.8;
}

/* 数据样式 */
.ontime-count {
  color: #67C23A;
  font-weight: 600;
}

.early-count {
  color: #E6A23C;
  font-weight: 600;
}

.late-count {
  color: #F56C6C;
  font-weight: 600;
}

.delay-time {
  color: #F56C6C;
  font-weight: 600;
}

.early-time {
  color: #E6A23C;
  font-weight: 600;
}

.ontime-time {
  color: #67C23A;
  font-weight: 600;
}

.late-time {
  color: #F56C6C;
  font-weight: 600;
}

/* 详情弹窗 */
.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

/* 树形组件样式 */
.dept-tree {
  background: transparent;
  color: #e5e7eb;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
}

.tree-node {
  display: flex;
  align-items: center;
  font-size: 14px;
  flex: 1;
}

.route-icon {
  margin-right: 8px;
  color: #60a5fa;
  font-size: 16px;
  flex-shrink: 0;
}

.station-icon {
  margin-right: 8px;
  color: #E6A23C;
  font-size: 16px;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  min-width: 0;
  white-space: nowrap;
}

.node-label.route {
  font-weight: 500;
  color: #f1f5f9;
}

.node-label.station {
  color: #94a3b8;
}

/* Element UI组件覆盖 */
:deep(.el-form-item__label) {
  color: #e5e7eb !important;
}

:deep(.el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e5e7eb !important;
}

:deep(.el-input__inner) {
  color: #e5e7eb !important;
}

:deep(.el-input__inner::placeholder) {
  color: #64748b !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
}

:deep(.el-table) {
  background: transparent !important;
  color: #e5e7eb !important;
  height: 100% !important;
  flex: 1 !important;
}

:deep(.el-table tr) {
  background: rgba(15, 23, 42, 0.6) !important;
}

:deep(.el-table tr:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

:deep(.el-table th.el-table__cell) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-table td.el-table__cell) {
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e5e7eb !important;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.8);
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  background: transparent;
  border: none;
  color: #94a3b8;
  transition: all 0.3s ease;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item:hover) {
  background: rgba(59, 130, 246, 0.1);
  color: #93c5fd;
}

/* Element Tree组件覆盖样式 */
:deep(.el-tree-node) {
  background: transparent;
}

:deep(.el-tree-node:hover > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 6px;
}

:deep(.el-tree-node__content) {
  background: transparent !important;
  border: none !important;
  color: #e5e7eb !important;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

:deep(.el-tree-node__content:hover) {
  background-color: rgba(59, 130, 246, 0.15) !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
}

:deep(.el-tree-node__expand-icon) {
  color: #64748b !important;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree .el-tree-node__children) {
  overflow: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .arrival-ontime-analysis {
    padding: 10px;
  }

  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .chart-card {
    height: 300px;
  }

  .chart-container {
    height: 220px;
  }
}

.dept-icon {
  margin-right: 8px;
  color: #a78bfa;
  font-size: 16px;
  flex-shrink: 0;
}

.node-label.dept {
  color: #cbd5e1;
  font-weight: 600;
}

/* 准点率详情弹窗样式 */
.ontime-detail-content {
  padding: 10px 0;
}

.summary-info {
  margin-bottom: 20px;
  padding: 16px;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.summary-item {
  text-align: center;
  padding: 12px;
  background: rgba(15, 23, 42, 0.4);
  border-radius: 6px;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

.summary-label {
  display: block;
  font-size: 12px;
  color: #94a3b8;
  margin-bottom: 8px;
  font-weight: 500;
}

.summary-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #e5e7eb;
}

.summary-value.planned {
  color: #409EFF;
}

.summary-value.actual {
  color: #909399;
}

.summary-value.ontime {
  color: #67C23A;
}

.arrival-list {
  margin-top: 20px;
}

.arrival-list h4 {
  margin: 0 0 16px 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3);
}

.ontime-arrivals {
  color: #67C23A !important;
  font-weight: 600;
}

.late-arrivals {
  color: #F56C6C !important;
  font-weight: 600;
}

/* 让准点率单元格具有点击效果 */
.ontime-cell {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 8px;
  margin: -8px;
}

.ontime-cell:hover {
  background-color: rgba(59, 130, 246, 0.1);
  transform: scale(1.02);
}

.ontime-rate {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.ontime-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
}

.ontime-trips {
  color: #67C23A;
}

.late-trips {
  color: #F56C6C;
}

/* 每日统计弹窗样式 */
.daily-stats-content {
  padding: 10px 0;
}

.day-of-week {
  font-weight: 500;
  color: #409EFF;
}

/* Element Plus link 按钮样式覆盖 */
:deep(.el-button.is-link) {
  background: transparent !important;
  border: none !important;
  color: #409EFF !important;
  padding: 4px 8px !important;
}

:deep(.el-button.is-link:hover) {
  color: #66b1ff !important;
  background: rgba(64, 158, 255, 0.1) !important;
}
</style>
