<template>
  <div class='route-deviation-analysis'>
    <el-row :gutter='20'>
      <!-- 左侧路线筛选 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <Operation />
              </el-icon>
              <span>路线筛选</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入路线名称' prefix-icon='Search' clearable />
            </div>

            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            >
              <template #default="{ node, data }">
                <span class="tree-node">
                  <el-icon v-if="data.type === 'dept'" class="dept-icon">
                    <OfficeBuilding />
                  </el-icon>
                  <el-icon v-else-if="data.type === 'route'" class="route-icon">
                    <Connection />
                  </el-icon>
                  <span class="node-label" :class="data.type">{{ data.label }}</span>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>

      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 筛选条件区域 -->
          <div class='filter-section'>
            <el-card class='filter-card'>
              <!-- 时间分析选择器 -->
              <TimeAnalysisSelector
                :initialAnalysisType="queryParams.analysisType"
                :availableTypes="['0', '2', '3']"
                @params-change="handleTimeAnalysisChange"
                ref="timeAnalysisSelectorRef"
              >
                <template #actions>
                  <el-form-item label="偏移等级" prop="deviationLevel">
                    <el-select v-model="queryParams.deviationLevel" placeholder="请选择等级" clearable style="width: 120px">
                      <el-option label="全部" value="" />
                      <el-option label="正常" value="normal" />
                      <el-option label="轻微偏移" value="light" />
                      <el-option label="严重偏移" value="severe" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
                    <el-button @click="resetQuery" icon="Refresh">重置</el-button>
                  </el-form-item>
                </template>
              </TimeAnalysisSelector>
            </el-card>
          </div>

          <!-- Tab切换区域 -->
          <div class='tabs-section'>
            <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
              <!-- 图表分析 -->
              <el-tab-pane label="图表分析" name="charts">
                <div class="tab-content">
                  <!-- 统计卡片区域 -->
                  <div class='stats-section'>
                    <el-row :gutter='16'>
                      <el-col :span='6'>
                        <div class='stat-card total'>
                          <div class='stat-icon'>
                            <el-icon><Connection /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.totalRoutes }}</div>
                            <div class='stat-label'>总线路数</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card average'>
                          <div class='stat-icon'>
                            <el-icon><TrendCharts /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.avgNormalRate }}%</div>
                            <div class='stat-label'>平均正常率</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card highest'>
                          <div class='stat-icon'>
                            <el-icon><TopRight /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.bestNormalRate }}%</div>
                            <div class='stat-label'>最高正常率</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card monitoring'>
                          <div class='stat-icon'>
                            <el-icon><Operation /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.totalMonitoring }}</div>
                            <div class='stat-label'>总监控次数</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 图表区域 -->
                  <div class='charts-section'>
                    <el-row :gutter='16'>
                      <el-col :span='24'>
                        <div class='chart-card'>
                          <div class='chart-header'>
                            <h3>路线偏移率趋势图 - {{ getAnalysisTypeText(queryParams.analysisType) }}</h3>
                          </div>
                          <div ref="deviationChartRef" class='chart-container'></div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 明细表格 -->
              <el-tab-pane label="明细表格" name="table">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>路线偏移率明细表</h4>
                        <span class="table-subtitle">点击数据格子查看详细信息</span>
                      </div>
                      <div class="table-actions">
                        <el-button type="success" size="small" icon="Download" @click="handleExport">导出表格</el-button>
                      </div>
                    </div>
                    <el-table :data="detailTableData" @selection-change="handleSelectionChange" style="width: 100%" height="580" border>
                      <el-table-column type="selection" width="55" align="center" fixed="left" />
                      <el-table-column label="路线" align="center" prop="name" width="140" fixed="left" />
                      <el-table-column label="类型" align="center" prop="type" width="80" fixed="left">
                        <template #default="scope">
                          <el-tag type="primary" size="small">
                            路线
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="总正常率" align="center" prop="totalNormalRate" width="120" fixed="left">
                        <template #default="scope">
                          <el-tag :type="getNormalRateType(scope.row.totalNormalRate)" size="small">
                            {{ scope.row.totalNormalRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="总监控数" align="center" prop="totalMonitoring" width="120" fixed="left" />

                      <!-- 动态生成的日期列 -->
                      <el-table-column
                        v-for="dateCol in dynamicDateColumns"
                        :key="dateCol.prop"
                        :label="dateCol.label"
                        align="center"
                        :prop="dateCol.prop"
                      >
                        <template #default="scope">
                          <div class="deviation-cell" @click="handleDeviationDetail(scope.row, dateCol)">
                            <div class="normal-rate">{{ scope.row[dateCol.rateProp] || '-' }}%</div>
                            <div class="deviation-info">
                              <span class="normal-count">正常: {{ scope.row[dateCol.normalProp] || 0 }}</span>
                              <span class="deviation-count">偏移: {{ scope.row[dateCol.deviationProp] || 0 }}</span>
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="total > 0"
                      :total="total"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getList"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 详情弹窗 -->
    <el-dialog :title="dialogTitle" v-model="showDetailDialog" width="800px" append-to-body>
      <div v-if="detailData" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item v-if="detailData.routeName" label="线路名称">{{ detailData.routeName }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.routeNumber" label="线路编号">{{ detailData.routeNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.plateNumber" label="车牌号">{{ detailData.plateNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.vehicleNumber" label="车辆编号">{{ detailData.vehicleNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.driverName" label="驾驶员">{{ detailData.driverName }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.statisticDate" label="统计日期">{{ detailData.statisticDate }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.monitoringCount || detailData.detectionCount" label="监控次数">{{ detailData.monitoringCount || detailData.detectionCount }}次</el-descriptions-item>
          <el-descriptions-item v-if="detailData.deviationCount" label="偏移次数">{{ detailData.deviationCount }}次</el-descriptions-item>
          <el-descriptions-item v-if="detailData.normalRate || detailData.deviationRate" label="正常率">
            <el-tag :type="getNormalRateType(detailData.normalRate || (100 - detailData.deviationRate))" size="small">
              {{ detailData.normalRate || (100 - detailData.deviationRate) }}%
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="detailData.avgDeviationDistance" label="平均偏移距离">{{ detailData.avgDeviationDistance }}m</el-descriptions-item>
          <el-descriptions-item v-if="detailData.startStation" label="起点站">{{ detailData.startStation }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.endStation" label="终点站">{{ detailData.endStation }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.normalTrips" label="正常班次">{{ detailData.normalTrips }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.deviationTrips" label="偏移班次">{{ detailData.deviationTrips }}</el-descriptions-item>
          <el-descriptions-item label="备注" span="2">{{ detailData.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 每日统计弹窗（月/年维度使用） -->
    <el-dialog
      v-model="showDailyStatsDialog"
      :title="dailyStatsTitle"
      width="1000px"
      :destroy-on-close="true"
    >
      <div v-if="dailyStatsData" class="daily-stats-content">
        <el-table
          :data="dailyStatsData.dailyRecords"
          border
          size="small"
          max-height="500"
          style="width: 100%"
        >
          <el-table-column label="日期" prop="date" align="center" />
          <el-table-column label="星期" prop="dayOfWeek" align="center">
            <template #default="scope">
              <span class="day-of-week">星期{{ scope.row.dayOfWeek }}</span>
            </template>
          </el-table-column>
          <el-table-column label="计划监控" prop="plannedMonitoring" align="center" />
          <el-table-column label="实际监控" prop="actualMonitoring" align="center" />
          <el-table-column label="正常班次" prop="normalTrips" align="center">
            <template #default="scope">
              <span class="normal-trips">{{ scope.row.normalTrips }}</span>
            </template>
          </el-table-column>
          <el-table-column label="偏移班次" prop="deviationTrips" align="center">
            <template #default="scope">
              <span class="deviation-trips">{{ scope.row.deviationTrips }}</span>
            </template>
          </el-table-column>
          <el-table-column label="正常率" prop="normalRate" align="center">
            <template #default="scope">
              <el-tag :type="getNormalRateType(scope.row.normalRate)" size="small">
                {{ scope.row.normalRate }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template #default="scope">
              <el-button type="primary" link size="small" @click="handleDailyStatsDetail(scope.row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDailyStatsDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 偏移详情弹窗 -->
    <el-dialog
      v-model="showDeviationDetailDialog"
      :title="deviationDetailTitle"
      width="1100px"
      :destroy-on-close="true"
    >
      <div v-if="deviationDetailData" class="deviation-detail-content">
        <!-- 汇总信息 -->
        <div class="summary-info">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="summary-item">
                <span class="summary-label">计划监控</span>
                <span class="summary-value planned">{{ deviationDetailData.plannedMonitoring }}次</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <span class="summary-label">实际监控</span>
                <span class="summary-value actual">{{ deviationDetailData.actualMonitoring }}次</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <span class="summary-label">正常班次</span>
                <span class="summary-value normal">{{ deviationDetailData.normalTrips }}次</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <span class="summary-label">正常率</span>
                <el-tag :type="getNormalRateType(deviationDetailData.normalRate)" size="small">
                  {{ deviationDetailData.normalRate }}%
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 偏移明细列表 -->
        <div class="deviation-list">
          <h4>当日偏移明细</h4>
          <el-table
            :data="deviationDetailData.deviationList"
            border
            size="small"
            max-height="400"
            style="width: 100%"
          >
            <el-table-column label="车牌号" prop="plateNumber" width="100" align="center" />
            <el-table-column label="上下行" prop="direction" width="80" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.direction === '上行' ? 'primary' : 'success'" size="small">
                  {{ scope.row.direction }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="起点站" prop="startStation" width="120" align="center" />
            <el-table-column label="终点站" prop="endStation" width="120" align="center" />
            <el-table-column label="偏移站点" prop="deviationStation" width="120" align="center" />
            <el-table-column label="偏移距离" prop="deviationDistance" width="100" align="center">
              <template #default="scope">
                <span :class="getDeviationDistanceClass(scope.row.deviationDistance)">
                  {{ scope.row.deviationDistance }}米
                </span>
              </template>
            </el-table-column>
            <el-table-column label="偏移状态" prop="status" width="80" align="center">
              <template #default="scope">
                <el-tag :type="getDeviationStatusType(scope.row.status)" size="small">
                  {{ getDeviationStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="驾驶员" prop="driverName" width="100" align="center" />
            <el-table-column label="备注" prop="remark" min-width="120" show-overflow-tooltip />
          </el-table>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDeviationDetailDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="RouteDeviationAnalysis">
import { ref, reactive, onMounted, nextTick, watchEffect, computed } from 'vue';
import { Operation, Connection, OfficeBuilding, TrendCharts, TopRight } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import TimeAnalysisSelector from '@/components/TimeAnalysisSelector/index.vue';
import Pagination from '@/components/Pagination/index.vue';

const loading = ref(true);
const showDetailDialog = ref(false);
const detailData = ref(null);
const activeTab = ref('charts');

// 树形相关
const deptName = ref('');
const deptTreeRef = ref();

// 图表引用
const deviationChartRef = ref(null);
let deviationChart = null;

// 表单引用
const queryRef = ref();
const timeAnalysisSelectorRef = ref();

// 分页数据
const total = ref(0);

// 表格数据
const detailTableData = ref([]);
const dynamicDateColumns = ref([]);

// 统计数据
const summaryStats = ref({
  totalRoutes: '28',
  avgNormalRate: '87.2',
  bestNormalRate: '96.8',
  totalMonitoring: '15,680'
});

// 偏移详情弹窗变量
const showDeviationDetailDialog = ref(false);
const deviationDetailData = ref(null);

// 偏移详情弹窗标题
const deviationDetailTitle = computed(() => {
  if (!deviationDetailData.value) return '偏移详情';
  return `${deviationDetailData.value.name} - ${deviationDetailData.value.date} 偏移详情`;
});

// 每日统计弹窗变量
const showDailyStatsDialog = ref(false);
const dailyStatsData = ref(null);
const dailyStatsTitle = ref('');

// 判断是否为月/年维度
const isMonthYearDimension = computed(() => {
  return queryParams.analysisType === '0' || queryParams.analysisType === '2'; // 年度或月度
});

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  analysisType: '3', // 默认日分析
  startTime: undefined,
  endTime: undefined,
  dateRange: null,
  deviationLevel: null,
  routeId: null,
  deptId: null
});

// 线路和车辆树数据
const deptOptions = ref([]);

// 计算弹窗标题
const dialogTitle = computed(() => {
  if (!detailData.value) return '详情';
  if (detailData.value.routeName && !detailData.value.plateNumber) {
    return `${detailData.value.routeName} - 线路偏移详情`;
  }
  if (detailData.value.plateNumber) {
    return `${detailData.value.plateNumber} - 车辆偏移详情`;
  }
  return '路线偏移详情';
});

// 动态生成日期列
function generateDateColumns() {
  dynamicDateColumns.value = [];
  if (!queryParams.startTime || !queryParams.endTime) {
    return;
  }

  const start = new Date(queryParams.startTime);
  const end = new Date(queryParams.endTime);
  let index = 1;

  switch(queryParams.analysisType) {
    case '0': // 年度
      for (let year = start.getFullYear(); year <= end.getFullYear(); year++) {
        dynamicDateColumns.value.push({
          prop: `date${index}`,
          label: `${year}年`,
          rateProp: `rate_${index}`,
          normalProp: `normal_${index}`,
          deviationProp: `deviation_${index}`,
          totalProp: `total_${index}`
        });
        index++;
      }
      break;
    case '2': // 月度
      for (let d = new Date(start.getFullYear(), start.getMonth(), 1); d <= end; d.setMonth(d.getMonth() + 1)) {
        dynamicDateColumns.value.push({
          prop: `date${index}`,
          label: `${d.getMonth() + 1}月`,
          rateProp: `rate_${index}`,
          normalProp: `normal_${index}`,
          deviationProp: `deviation_${index}`,
          totalProp: `total_${index}`
        });
        index++;
      }
      break;
    case '3': // 日
      for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
        dynamicDateColumns.value.push({
          prop: `date${index}`,
          label: `${d.getMonth() + 1}/${d.getDate()}`,
          rateProp: `rate_${index}`,
          normalProp: `normal_${index}`,
          deviationProp: `deviation_${index}`,
          totalProp: `total_${index}`
        });
        index++;
      }
      break;
  }
}

onMounted(() => {
  getTreeSelect();
  handleTabChange(activeTab.value);
  nextTick(() => {
    initCharts();
  });
});

function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** 查询组织机构和路线下拉树结构 */
async function getTreeSelect() {
  try {
    deptOptions.value = [
      {
        id: 1,
        label: '总公司',
        type: 'dept',
        children: [
          {
            id: 2,
            label: '运营部',
            type: 'dept',
            children: [
              { id: 11, label: '115路', type: 'route', routeNumber: '115', normalRate: 87.8, deptId: 2 },
              { id: 12, label: '135路', type: 'route', routeNumber: '135', normalRate: 82.4, deptId: 2 },
              { id: 13, label: '201路', type: 'route', routeNumber: '201', normalRate: 94.2, deptId: 2 }
            ]
          },
          {
            id: 3,
            label: '第二运营部',
            type: 'dept',
            children: [
              { id: 14, label: '301路', type: 'route', routeNumber: '301', normalRate: 85.7, deptId: 3 },
              { id: 15, label: '401路', type: 'route', routeNumber: '401', normalRate: 89.3, deptId: 3 }
            ]
          },
          {
            id: 4,
            label: '第三运营部',
            type: 'dept',
            children: [
              { id: 16, label: '501路', type: 'route', routeNumber: '501', normalRate: 83.9, deptId: 4 },
              { id: 17, label: '601路', type: 'route', routeNumber: '601', normalRate: 91.5, deptId: 4 }
            ]
          }
        ]
      }
    ];
  } catch (error) {
    console.error('获取组织机构路线树失败:', error);
  }
}

/** 时间分析参数变化处理 */
function handleTimeAnalysisChange(params) {
  queryParams.analysisType = params.analysisType;
  queryParams.startTime = params.startTime;
  queryParams.endTime = params.endTime;

  // 生成动态日期列
  generateDateColumns();

  // 更新统计数据和图表
  updateStatsData();
  if (activeTab.value === 'charts') {
    nextTick(() => {
      updateDeviationChart();
    });
  }

  // 重新加载表格数据
  if (activeTab.value === 'table') {
    getList();
  }
}

/** 更新统计数据 */
function updateStatsData() {
  // 根据选中的时间范围和类型更新统计数据
  const analysisText = getAnalysisTypeText(queryParams.analysisType);
  const timeRange = getTimeRangeText();

  // 更新统计卡片数据
  summaryStats.value = {
    totalRoutes: '28',
    avgNormalRate: '87.2',
    bestNormalRate: '96.8',
    totalMonitoring: '15,680'
  };
}

/** 获取分析类型文本 */
function getAnalysisTypeText(type) {
  const typeMap = {
    '0': '年度统计',
    '2': '月度统计',
    '3': '日统计'
  };
  return typeMap[type] || '日统计';
}

/** 获取时间范围文本 */
function getTimeRangeText() {
  if (!queryParams.startTime || !queryParams.endTime) {
    return '未选择时间范围';
  }

  const startTime = new Date(queryParams.startTime);
  const endTime = new Date(queryParams.endTime);

  switch(queryParams.analysisType) {
    case '0': // 年度
      return `${startTime.getFullYear()}年 - ${endTime.getFullYear()}年`;
    case '2': // 月度
      return `${startTime.getFullYear()}-${(startTime.getMonth() + 1).toString().padStart(2, '0')} 至 ${endTime.getFullYear()}-${(endTime.getMonth() + 1).toString().padStart(2, '0')}`;
    case '3': // 日
      return `${startTime.toISOString().split('T')[0]} 至 ${endTime.toISOString().split('T')[0]}`;
    default:
      return '未知时间范围';
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  if (data.type === 'route') {
    queryParams.routeId = data.id;
    queryParams.routeNumber = data.routeNumber;
    queryParams.deptId = data.deptId;
  } else if (data.type === 'dept') {
    queryParams.deptId = data.id;
    queryParams.routeId = null;
    queryParams.routeNumber = null;
  }
  handleQuery();
}

/** Tab切换事件 */
function handleTabChange(tabName) {
  queryParams.pageNum = 1;
  activeTab.value = tabName;
  loading.value = true;

  switch (tabName) {
    case 'charts':
      nextTick(() => {
        updateDeviationChart();
      });
      loading.value = false;
      break;
    case 'table':
      generateDateColumns();
      getList();
      break;
  }
}

// 获取表格数据
function getList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateTableData();
    detailTableData.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 生成表格模拟数据
function generateTableData() {
  const routes = [
    { name: '115路', type: '路线', routeNumber: '115', totalNormalRate: '87.8', totalMonitoring: '1,250' },
    { name: '135路', type: '路线', routeNumber: '135', totalNormalRate: '82.4', totalMonitoring: '980' },
    { name: '201路', type: '路线', routeNumber: '201', totalNormalRate: '94.2', totalMonitoring: '1,680' },
    { name: '301路', type: '路线', routeNumber: '301', totalNormalRate: '85.7', totalMonitoring: '1,420' },
    { name: '401路', type: '路线', routeNumber: '401', totalNormalRate: '89.3', totalMonitoring: '1,150' },
    { name: '501路', type: '路线', routeNumber: '501', totalNormalRate: '83.9', totalMonitoring: '1,320' },
    { name: '601路', type: '路线', routeNumber: '601', totalNormalRate: '91.5', totalMonitoring: '1,580' }
  ];

  return routes.map((item, index) => {
    const rowData = {
      id: index + 1,
      ...item
    };

    // 为每个动态列生成数据
    dynamicDateColumns.value.forEach((col, colIndex) => {
      const totalTrips = Math.floor(Math.random() * 50 + 30);
      const normalTrips = Math.floor(totalTrips * (0.75 + Math.random() * 0.25));
      const deviationTrips = totalTrips - normalTrips;
      const rate = totalTrips > 0 ? ((normalTrips / totalTrips) * 100).toFixed(1) : '0.0';

      rowData[col.rateProp] = rate;
      rowData[col.normalProp] = normalTrips;
      rowData[col.deviationProp] = deviationTrips;
      rowData[col.totalProp] = totalTrips;
    });

    return rowData;
  });
}

// 初始化图表
function initCharts() {
  initDeviationChart();
}

// 初始化偏移率图表
function initDeviationChart() {
  if (!deviationChartRef.value) return;

  deviationChart = echarts.init(deviationChartRef.value);
  updateDeviationChart();
}

// 更新偏移率图表
function updateDeviationChart() {
  if (!deviationChart) return;

  const analysisType = queryParams.analysisType;
  let xAxisData = [];
  let normalData = [];
  let deviationData = [];

  if (!queryParams.startTime || !queryParams.endTime) {
    xAxisData = ['1月', '2月', '3月', '4月', '5月', '6月'];
    normalData = [87.2, 84.8, 91.1, 85.5, 89.7, 92.9];
    deviationData = [12.8, 15.2, 8.9, 14.5, 10.3, 7.1];
  } else {
    const start = new Date(queryParams.startTime);
    const end = new Date(queryParams.endTime);

    switch(analysisType) {
      case '0': // 年
        for (let year = start.getFullYear(); year <= end.getFullYear(); year++) {
          xAxisData.push(`${year}年`);
        }
        break;
      case '2': // 月
        for (let d = new Date(start.getFullYear(), start.getMonth(), 1); d <= end; d.setMonth(d.getMonth() + 1)) {
          xAxisData.push(`${d.getMonth() + 1}月`);
        }
        break;
      case '3': // 日
        for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
          xAxisData.push(`${d.getMonth() + 1}/${d.getDate()}`);
        }
        break;
      default:
        xAxisData = ['1月', '2月', '3月', '4月', '5月', '6月'];
    }

    normalData = generateSeriesData(xAxisData.length, 75, 95);
    deviationData = normalData.map(rate => (100 - parseFloat(rate)).toFixed(1));
  }

  function generateSeriesData(length, min, max) {
    const data = [];
    for (let i = 0; i < length; i++) {
      data.push((Math.random() * (max - min) + min).toFixed(1));
    }
    return data;
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' }
    },
    legend: {
      data: ['正常率', '偏移率'],
      textStyle: { color: '#e5e7eb' },
      top: '5%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: { color: '#94a3b8' },
      axisLine: { lineStyle: { color: '#374151' } }
    },
    yAxis: {
      type: 'value',
      name: '百分比(%)',
      nameTextStyle: { color: '#94a3b8' },
      axisLabel: { color: '#94a3b8' },
      axisLine: { lineStyle: { color: '#374151' } },
      splitLine: { lineStyle: { color: '#374151' } }
    },
    series: [
      {
        name: '正常率',
        type: 'line',
        data: normalData,
        smooth: true,
        itemStyle: { color: '#67C23A' },
        lineStyle: { color: '#67C23A' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
            { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
          ])
        }
      },
      {
        name: '偏移率',
        type: 'line',
        data: deviationData,
        smooth: true,
        itemStyle: { color: '#F56C6C' },
        lineStyle: { color: '#F56C6C' }
      }
    ]
  };

  deviationChart.setOption(option);
}

// 查询
function handleQuery() {
  queryParams.pageNum = 1;
  // 更新统计数据
  updateStatsData();
  generateDateColumns();
  handleTabChange(activeTab.value);
}

// 重置
function resetQuery() {
  queryParams.deviationLevel = null;
  queryParams.routeId = null;
  queryParams.deptId = null;
  timeAnalysisSelectorRef.value?.reset();
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

// 表格选择
function handleSelectionChange(selection) {
  // 处理表格选择
}

// 查看详情
function handleDetail(row) {
  detailData.value = row;
  showDetailDialog.value = true;
}

// 处理偏移详情点击
function handleDeviationDetail(row, dateCol) {
  if (isMonthYearDimension.value) {
    // 月/年维度: 显示每日统计数据表格
    const dailyStats = generateDailyStatsData(row, dateCol);
    dailyStatsData.value = dailyStats;
    dailyStatsTitle.value = `${row.name} - ${dateCol.label} 每日偏移统计`;
    showDailyStatsDialog.value = true;
  } else {
    // 日维度: 直接显示偏移明细
    const detailData = generateDeviationDetailData(row, dateCol);
    deviationDetailData.value = detailData;
    showDeviationDetailDialog.value = true;
  }
}

// 生成偏移详情模拟数据
function generateDeviationDetailData(row, dateCol) {
  const plannedMonitoring = Math.floor(Math.random() * 15 + 10); // 10-25次监控
  const actualMonitoring = Math.floor(plannedMonitoring * (0.95 + Math.random() * 0.05));
  const normalTrips = Math.floor(actualMonitoring * (0.75 + Math.random() * 0.20));
  const deviationTrips = actualMonitoring - normalTrips;

  const normalRate = actualMonitoring > 0 ? ((normalTrips / actualMonitoring) * 100).toFixed(1) : '0.0';

  // 生成偏移明细列表
  const deviationList = [];
  const drivers = ['张师傅', '李师傅', '王师傅', '陈师傅', '刘师傅', '赵师傅'];

  for (let i = 0; i < deviationTrips; i++) {
    const scheduledHour = 6 + Math.floor(i * 14 / deviationTrips); // 6:00-20:00分布
    const scheduledMinute = Math.floor(Math.random() * 60);

    // 偏移距离：10-200米
    const deviationDistance = Math.floor(Math.random() * 190 + 10);
    let status;
    if (deviationDistance < 50) status = 'normal';
    else if (deviationDistance < 150) status = 'light';
    else status = 'severe';

    // 随机生成上下行和站点信息
    const isUpDirection = Math.random() > 0.5;
    const direction = isUpDirection ? '上行' : '下行';
    const stations = {
      '上行': { start: '火车站', end: '汽车站' },
      '下行': { start: '汽车站', end: '火车站' }
    };

    deviationList.push({
      plateNumber: `赣A${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${(1000 + Math.floor(Math.random() * 9000)).toString()}`,
      direction: direction,
      startStation: stations[direction].start,
      endStation: stations[direction].end,
      deviationStation: `${stations[direction].start}后第${Math.floor(Math.random() * 3 + 1)}站`,
      deviationDistance: deviationDistance,
      status: status,
      driverName: drivers[Math.floor(Math.random() * drivers.length)],
      remark: Math.random() > 0.7 ? (status === 'severe' ? '需要关注' : status === 'light' ? '轻微偏移' : '正常') : ''
    });
  }

  return {
    name: row.name,
    type: row.type,
    date: dateCol.label,
    plannedMonitoring: plannedMonitoring,
    actualMonitoring: actualMonitoring,
    normalTrips: normalTrips,
    deviationTrips: deviationTrips,
    normalRate: normalRate,
    deviationList: deviationList
  };
}

// 生成每日统计数据（月/年维度点击格子后显示）
function generateDailyStatsData(row, dateCol) {
  const dailyRecords = [];

  // 根据分析维度确定日期范围
  let startDate, endDate;

  if (queryParams.analysisType === '0') {
    // 年度：生成该年份每天的数据
    const year = parseInt(dateCol.label.replace('年', ''));
    startDate = new Date(year, 0, 1);
    endDate = new Date(year, 11, 31);
  } else if (queryParams.analysisType === '2') {
    // 月度：生成该月每天的数据
    const month = parseInt(dateCol.label.replace('月', '')) - 1;
    const year = queryParams.startTime ? new Date(queryParams.startTime).getFullYear() : new Date().getFullYear();
    startDate = new Date(year, month, 1);
    endDate = new Date(year, month + 1, 0);
  }

  // 生成每日记录
  for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
    const plannedMonitoring = Math.floor(Math.random() * 20 + 15);
    const actualMonitoring = Math.floor(plannedMonitoring * (0.95 + Math.random() * 0.05));
    const normalTrips = Math.floor(actualMonitoring * (0.75 + Math.random() * 0.25));
    const normalRate = actualMonitoring > 0 ? ((normalTrips / actualMonitoring) * 100).toFixed(1) : '0.0';

    dailyRecords.push({
      id: `${d.getFullYear()}${(d.getMonth() + 1).toString().padStart(2, '0')}${d.getDate().toString().padStart(2, '0')}`,
      date: `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')}`,
      dayOfWeek: ['日', '一', '二', '三', '四', '五', '六'][d.getDay()],
      plannedMonitoring: plannedMonitoring,
      actualMonitoring: actualMonitoring,
      normalTrips: normalTrips,
      deviationTrips: actualMonitoring - normalTrips,
      normalRate: normalRate,
      routeName: row.name,
      routeNumber: row.routeNumber
    });
  }

  return {
    routeName: row.name,
    period: dateCol.label,
    dailyRecords: dailyRecords.sort((a, b) => new Date(b.date) - new Date(a.date))
  };
}

// 处理每日统计详情点击
function handleDailyStatsDetail(dailyRecord) {
  // 生成该日期的偏移明细
  const detailData = generateDailyDeviationDetail(dailyRecord);
  deviationDetailData.value = detailData;
  showDeviationDetailDialog.value = true; // 打开偏移明细弹窗
}

// 生成每日偏移明细数据
function generateDailyDeviationDetail(record) {
  const deviationList = [];
  const drivers = ['张师傅', '李师傅', '王师傅', '陈师傅', '刘师傅', '赵师傅'];

  for (let i = 0; i < record.deviationTrips; i++) {
    const scheduledHour = 6 + Math.floor(i * 14 / record.deviationTrips);
    const scheduledMinute = Math.floor(Math.random() * 60);

    const deviationDistance = Math.floor(Math.random() * 190 + 10);
    let status;
    if (deviationDistance < 50) status = 'normal';
    else if (deviationDistance < 150) status = 'light';
    else status = 'severe';

    // 随机生成上下行和站点信息
    const isUpDirection = Math.random() > 0.5;
    const direction = isUpDirection ? '上行' : '下行';
    const stations = {
      '上行': { start: '火车站', end: '汽车站' },
      '下行': { start: '汽车站', end: '火车站' }
    };

    deviationList.push({
      plateNumber: `赣A${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${(1000 + Math.floor(Math.random() * 9000)).toString()}`,
      direction: direction,
      startStation: stations[direction].start,
      endStation: stations[direction].end,
      deviationStation: `${stations[direction].start}后第${Math.floor(Math.random() * 3 + 1)}站`,
      deviationDistance: deviationDistance,
      status: status,
      driverName: drivers[Math.floor(Math.random() * drivers.length)],
      remark: Math.random() > 0.7 ? (status === 'severe' ? '需要关注' : status === 'light' ? '轻微偏移' : '正常') : ''
    });
  }

  return {
    name: record.routeName,
    date: record.date,
    plannedMonitoring: record.plannedMonitoring,
    actualMonitoring: record.actualMonitoring,
    normalTrips: record.normalTrips,
    normalRate: record.normalRate,
    deviationList: deviationList.sort((a, b) => a.plateNumber.localeCompare(b.plateNumber))
  };
}

// 导出表格
function handleExport() {
  ElMessage.success('导出成功');
}

// 获取正常率类型
function getNormalRateType(rate) {
  const numRate = parseFloat(rate);
  if (numRate >= 95) return 'success';
  if (numRate >= 85) return 'info';
  if (numRate >= 75) return 'warning';
  return 'danger';
}

// 获取偏移距离样式类
function getDeviationDistanceClass(distance) {
  const numDistance = parseFloat(distance);
  if (numDistance < 50) return 'distance-normal';
  if (numDistance < 150) return 'distance-light';
  return 'distance-severe';
}

// 获取偏移状态类型
function getDeviationStatusType(status) {
  const typeMap = {
    'normal': 'success',
    'light': 'warning',
    'severe': 'danger'
  };
  return typeMap[status] || 'info';
}

// 获取偏移状态文本
function getDeviationStatusText(status) {
  const textMap = {
    'normal': '正常',
    'light': '轻微偏移',
    'severe': '严重偏移'
  };
  return textMap[status] || '未知';
}
</script>

<style scoped>
.route-deviation-analysis {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 左侧面板样式 */
.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.search-box {
  margin-bottom: 16px;
}

/* 右侧内容区域 */
.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 筛选条件区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  background: rgba(15, 23, 42, 0.98) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* Tab区域 */
.tabs-section {
  margin-bottom: 20px;
}

.tab-content {
  padding-top: 20px;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(41, 52, 70, 0.6) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.4);
}

.stat-card.total {
  border-left: 4px solid #409EFF;
}

.stat-card.average {
  border-left: 4px solid #67C23A;
}

.stat-card.highest {
  border-left: 4px solid #E6A23C;
}

.stat-card.monitoring {
  border-left: 4px solid #F56C6C;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #60a5fa;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #94a3b8;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  height: 400px;
}

.chart-card .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.chart-card .chart-header h3 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.chart-container {
  height: 320px;
  width: 100%;
}

/* 表格样式 */
.table-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 620px;
  height: calc(100vh - 480px);
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.table-header h4 {
  margin: 0 0 4px 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.table-subtitle {
  font-size: 12px;
  color: #94a3b8;
  font-weight: normal;
}

/* 数据样式 */
.normal-count {
  color: #67C23A;
  font-weight: 600;
}

.deviation-count {
  color: #F56C6C;
  font-weight: 600;
}

.distance-normal {
  color: #67C23A;
  font-weight: 600;
}

.distance-light {
  color: #E6A23C;
  font-weight: 600;
}

.distance-severe {
  color: #F56C6C;
  font-weight: 600;
}

/* 详情弹窗 */
.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

/* 树形组件样式 */
.dept-tree {
  background: transparent;
  color: #e5e7eb;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
}

.tree-node {
  display: flex;
  align-items: center;
  font-size: 14px;
  flex: 1;
}

.route-icon {
  margin-right: 8px;
  color: #60a5fa;
  font-size: 16px;
  flex-shrink: 0;
}

.dept-icon {
  margin-right: 8px;
  color: #a78bfa;
  font-size: 16px;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  min-width: 0;
  white-space: nowrap;
}

.node-label.route {
  font-weight: 500;
  color: #f1f5f9;
}

.node-label.dept {
  color: #cbd5e1;
  font-weight: 600;
}

/* Element UI组件覆盖 */
:deep(.el-form-item__label) {
  color: #e5e7eb !important;
}

:deep(.el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e5e7eb !important;
}

:deep(.el-input__inner) {
  color: #e5e7eb !important;
}

:deep(.el-input__inner::placeholder) {
  color: #64748b !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
}

:deep(.el-table) {
  background: transparent !important;
  color: #e5e7eb !important;
  height: 100% !important;
  flex: 1 !important;
}

:deep(.el-table tr) {
  background: rgba(15, 23, 42, 0.6) !important;
}

:deep(.el-table tr:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

:deep(.el-table th.el-table__cell) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-table td.el-table__cell) {
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e5e7eb !important;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.8);
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  background: transparent;
  border: none;
  color: #94a3b8;
  transition: all 0.3s ease;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item:hover) {
  background: rgba(59, 130, 246, 0.1);
  color: #93c5fd;
}

/* Element Tree组件覆盖样式 */
:deep(.el-tree-node) {
  background: transparent;
}

:deep(.el-tree-node:hover > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 6px;
}

:deep(.el-tree-node__content) {
  background: transparent !important;
  border: none !important;
  color: #e5e7eb !important;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

:deep(.el-tree-node__content:hover) {
  background-color: rgba(59, 130, 246, 0.15) !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
}

:deep(.el-tree-node__expand-icon) {
  color: #64748b !important;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree .el-tree-node__children) {
  overflow: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .route-deviation-analysis {
    padding: 10px;
  }

  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .chart-card {
    height: 300px;
  }

  .chart-container {
    height: 220px;
  }
}

/* 偏移详情弹窗样式 */
.deviation-detail-content {
  padding: 10px 0;
}

.summary-info {
  margin-bottom: 20px;
  padding: 16px;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.summary-item {
  text-align: center;
  padding: 12px;
  background: rgba(15, 23, 42, 0.4);
  border-radius: 6px;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

.summary-label {
  display: block;
  font-size: 12px;
  color: #94a3b8;
  margin-bottom: 8px;
  font-weight: 500;
}

.summary-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #e5e7eb;
}

.summary-value.planned {
  color: #409EFF;
}

.summary-value.actual {
  color: #909399;
}

.summary-value.normal {
  color: #67C23A;
}

.deviation-list {
  margin-top: 20px;
}

.deviation-list h4 {
  margin: 0 0 16px 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3);
}

.normal-trips {
  color: #67C23A !important;
  font-weight: 600;
}

.deviation-trips {
  color: #F56C6C !important;
  font-weight: 600;
}

/* 让偏移单元格具有点击效果 */
.deviation-cell {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 8px;
  margin: -8px;
}

.deviation-cell:hover {
  background-color: rgba(59, 130, 246, 0.1);
  transform: scale(1.02);
}

.normal-rate {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.deviation-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
}

/* 每日统计弹窗样式 */
.daily-stats-content {
  padding: 10px 0;
}

.day-of-week {
  font-weight: 500;
  color: #409EFF;
}

/* Element Plus link 按钮样式覆盖 */
:deep(.el-button.is-link) {
  background: transparent !important;
  border: none !important;
  color: #409EFF !important;
  padding: 4px 8px !important;
}

:deep(.el-button.is-link:hover) {
  color: #66b1ff !important;
  background: rgba(64, 158, 255, 0.1) !important;
}
</style>