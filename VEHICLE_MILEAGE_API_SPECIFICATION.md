# 车辆里程分析页面 - 后台接口数据格式规范

## 📋 接口概览

车辆里程分析页面需要以下几个主要接口：

1. **统计数据接口** - 获取汇总统计信息
2. **图表数据接口** - 获取趋势图表数据  
3. **明细表格接口** - 获取分页的明细数据
4. **车辆详情接口** - 获取单个车辆的详细信息
5. **驾驶员详情接口** - 获取车辆关联的驾驶员信息

## 🔍 1. 统计数据接口

### 接口地址
```
GET /api/analysis/vehicle-mileage/statistics
```

### 请求参数
```json
{
  "analysisType": "3",           // 分析类型：0-年度，2-月度，3-日，4-小时
  "startTime": "2024-01-01",     // 开始时间
  "endTime": "2024-01-31",       // 结束时间
  "deptId": 123,                 // 组织机构ID（可选）
  "vehicleId": 456,              // 车辆ID（可选）
  "plateNumber": "京A12345",     // 车牌号（可选）
  "vehicleNumber": "V001",       // 车辆编号（可选）
  "routeId": "115"               // 线路ID（可选）
}
```

### 响应数据
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalMileage": "125648.7",       // 总里程(公里)
    "operationalMileage": "89254.3",  // 运营里程(公里)
    "nonOperationalMileage": "36394.4", // 非运营里程(公里)
    "vehicleCount": 451               // 车辆数量
  }
}
```

## 📈 2. 图表数据接口

### 接口地址
```
GET /api/analysis/vehicle-mileage/chart-data
```

### 请求参数
```json
{
  "analysisType": "3",           // 分析类型
  "startTime": "2024-01-01",     // 开始时间
  "endTime": "2024-01-31",       // 结束时间
  "deptId": 123,                 // 组织机构ID（可选）
  "vehicleId": 456,              // 车辆ID（可选）
  "plateNumber": "京A12345",     // 车牌号（可选）
  "routeId": "115"               // 线路ID（可选）
}
```

### 响应数据
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "xAxisData": [                    // X轴数据（根据analysisType变化）
      "2024-01-01", "2024-01-02", "2024-01-03", "..."
    ],
    "series": [
      {
        "name": "运营里程",
        "type": "line",
        "data": [145.8, 162.3, 128.9, 178.5, 156.2]  // 对应X轴的运营里程数据
      },
      {
        "name": "非运营里程", 
        "type": "line",
        "data": [52.4, 45.7, 61.2, 48.9, 55.6]       // 对应X轴的非运营里程数据
      }
    ]
  }
}
```

## 📊 3. 明细表格接口

### 接口地址
```
GET /api/analysis/vehicle-mileage/detail-list
```

### 请求参数
```json
{
  "pageNum": 1,                  // 页码
  "pageSize": 10,                // 每页大小
  "analysisType": "3",           // 分析类型
  "startTime": "2024-01-01",     // 开始时间
  "endTime": "2024-01-31",       // 结束时间
  "deptId": 123,                 // 组织机构ID（可选）
  "plateNumber": "京A12345",     // 车牌号（可选）
  "vehicleNumber": "V001",       // 车辆编号（可选）
  "routeId": "115"               // 线路ID（可选）
}
```

### 响应数据
```json
{
  "code": 200,
  "msg": "操作成功", 
  "data": {
    "total": 256,
    "rows": [
      {
        "vehicleId": 1001,
        "plateNumber": "京A12345",
        "vehicleNumber": "V001",
        "deptName": "第一车队",
        "routeName": "115路",
        "averageSpeed": 42.8,          // 平均速度(km/h)
        "runningTime": 12.5,           // 运行时长(根据analysisType单位变化)
        "driverCount": 3,              // 驾驶员数
        "vehicleType": "公交车",       // 车辆类型
        "engineNumber": "ENG001",      // 发动机号
        "chassisNumber": "CHA001",     // 车架号
        "purchaseDate": "2020-05-15",  // 购买日期
        "lastMaintenanceDate": "2024-01-10", // 最后维护日期
        
        // 动态日期字段（根据时间范围生成）
        "operational_2024_01_01": 145.8,    // 2024-01-01运营里程
        "nonOperational_2024_01_01": 52.4,  // 2024-01-01非运营里程
        "operational_2024_01_02": 162.3,    // 2024-01-02运营里程
        "nonOperational_2024_01_02": 45.7,  // 2024-01-02非运营里程
        // ... 更多日期字段
        
        // 汇总字段
        "totalOperationalMileage": 4256.8,  // 总运营里程
        "totalNonOperationalMileage": 1456.2, // 总非运营里程
        "totalMileage": 5713.0             // 总里程
      }
      // ... 更多记录
    ]
  }
}
```

## 🚗 4. 车辆详情接口

### 接口地址
```
GET /api/analysis/vehicle-mileage/vehicle-detail/{vehicleId}
```

### 请求参数
```json
{
  "statisticDate": "2024-01-15"  // 统计日期
}
```

### 响应数据
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "vehicleId": 1001,
    "plateNumber": "京A12345",
    "vehicleNumber": "V001",
    "deptName": "第一车队",
    "routeName": "115路",
    "vehicleType": "公交车",
    "engineNumber": "ENG001",
    "chassisNumber": "CHA001",
    "purchaseDate": "2020-05-15",
    "lastMaintenanceDate": "2024-01-10",
    "operationalMileage": 145.8,   // 运营里程(km)
    "nonOperationalMileage": 52.4, // 非运营里程(km)
    "totalMileage": 198.2,         // 总里程(km)
    "averageSpeed": 42.8,          // 平均速度(km/h)
    "runningTime": 12.5,           // 运行时长
    "fuelConsumption": 68.5,       // 油耗(L)
    "maintenanceCost": 1250.0,     // 维护成本(元)
    "driverCount": 3,              // 驾驶员数量
    "routeDetail": "火车站-市中心-开发区-工业园", // 行驶路线
    "vehicleStatus": "normal",     // 车辆状态：normal-正常，maintenance-维修中，retired-报废
    "lastInspectionDate": "2024-01-05", // 最后检查日期
    "nextMaintenanceDate": "2024-02-10", // 下次维护日期
    "remark": "车况良好，按时保养"      // 备注
  }
}
```

## 👥 5. 驾驶员详情接口

### 接口地址
```
GET /api/analysis/vehicle-mileage/driver-detail/{vehicleId}
```

### 响应数据
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "vehicleId": 1001,
    "plateNumber": "京A12345",
    "drivers": [
      {
        "driverId": 2001,
        "name": "张志明",
        "employeeId": "D001",
        "phone": "13800138001",
        "status": "在岗",             // 状态：在岗、休假、离职
        "shift": "早班",             // 班次
        "drivingLicense": "A1",      // 驾驶证类型
        "hireDate": "2019-03-15",    // 入职日期
        "drivingExperience": 8,      // 驾龄(年)
        "mileageContribution": 1256.8, // 该司机贡献的里程
        "lastDriveTime": "2024-01-15 18:30:00"
      },
      {
        "driverId": 2002,
        "name": "李华强", 
        "employeeId": "D002",
        "phone": "13800138002",
        "status": "在岗",
        "shift": "中班",
        "drivingLicense": "A1",
        "hireDate": "2020-07-20",
        "drivingExperience": 6,
        "mileageContribution": 856.4,
        "lastDriveTime": "2024-01-15 14:45:00"
      }
    ]
  }
}
```

## 🔧 6. 辅助接口

### 6.1 线路选项接口
```
GET /api/basic/routes/options
```

响应：
```json
{
  "code": 200,
  "data": [
    {"label": "115路", "value": "115"},
    {"label": "135路", "value": "135"},
    {"label": "201路", "value": "201"},
    {"label": "202路", "value": "202"},
    {"label": "301路", "value": "301"}
  ]
}
```

### 6.2 组织机构树接口
```
GET /api/system/dept/tree
```

响应：
```json
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "label": "运输公司",
      "children": [
        {"id": 2, "label": "第一车队"},
        {"id": 3, "label": "第二车队"},
        {"id": 4, "label": "第三车队"},
        {"id": 5, "label": "机修组"}
      ]
    }
  ]
}
```

### 6.3 车辆类型选项接口
```
GET /api/basic/vehicles/types
```

响应：
```json
{
  "code": 200,
  "data": [
    {"label": "公交车", "value": "bus"},
    {"label": "中巴车", "value": "minibus"},
    {"label": "大巴车", "value": "coach"}
  ]
}
```

## 📝 重要说明

### 1. 动态日期字段生成规则
- 字段名格式：`operational_YYYY_MM_DD` 和 `nonOperational_YYYY_MM_DD`
- 根据 `analysisType` 和时间范围动态生成
- 年度分析：`operational_2024`, `nonOperational_2024`
- 月度分析：`operational_2024_01`, `nonOperational_2024_01`
- 日分析：`operational_2024_01_01`, `nonOperational_2024_01_01`
- 小时分析：`operational_2024_01_01_08`, `nonOperational_2024_01_01_08`

### 2. 分析类型说明
- `0`: 年度分析
- `2`: 月度分析  
- `3`: 日分析
- `4`: 小时分析

### 3. 车辆状态说明
- `normal`: 正常运营
- `maintenance`: 维修中
- `retired`: 已报废
- `standby`: 备用

### 4. 数据精度要求
- 里程数据保留1位小数
- 速度数据保留1位小数
- 油耗数据保留1位小数
- 成本数据保留2位小数

### 5. 性能考虑
- 建议对大数据量查询进行分页处理
- 图表数据建议限制在合理范围内（如最多31个数据点）
- 可考虑添加数据缓存机制
- 车辆里程数据通常比司机数据更稳定，可适当延长缓存时间

### 6. 业务逻辑说明
- 运营里程：车辆在正常运营状态下行驶的里程
- 非运营里程：车辆在非运营状态下行驶的里程（如回库、调度等）
- 总里程 = 运营里程 + 非运营里程
- 平均速度基于运营时间计算
- 驾驶员数量指该车辆的所有关联驾驶员
