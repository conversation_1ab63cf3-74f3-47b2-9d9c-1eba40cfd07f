export interface VehicleVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 车牌号
   */
  plateNumber: string;

  /**
   * 车牌颜色(blue:蓝牌,yellow:黄牌,green:绿牌,white:白牌,black:黑牌)
   */
  plateColor: string;

  /**
   * 车辆编号
   */
  vehicleNumber: string;

  /**
   * 品牌型号
   */
  brandModel: string;

  /**
   * 车辆类型(bus:公交车,electric_bus:电动公交,hybrid_bus:混合动力)
   */
  vehicleType: string;

  /**
   * 座位数
   */
  seatCount: string;

  /**
   * 车架号
   */
  vinNumber: string;

  /**
   * 购买日期
   */
  purchaseDate: string;

  /**
   * 上次维修时间
   */
  lastMaintenanceTime: string;

  /**
   * 总里程(公里)
   */
  totalMileage: string;

  /**
   * 主设备ID
   */
  mainDeviceId: string | number;

  /**
   * 子设备ID
   */
  subDeviceIds: string | number;

  /**
   * 状态(0:停用,1:正常,2:维修)
   */
  status: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 所属组织机构ID
   */
  deptId: string | number;

  /**
   * 所属组织机构名称
   */
  deptName: string;

}

export interface VehicleForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 车牌号
   */
  plateNumber?: string;

  /**
   * 车牌颜色(blue:蓝牌,yellow:黄牌,green:绿牌,white:白牌,black:黑牌)
   */
  plateColor?: string;

  /**
   * 车辆编号
   */
  vehicleNumber?: string;

  /**
   * 品牌型号
   */
  brandModel?: string;

  /**
   * 车辆类型(bus:公交车,electric_bus:电动公交,hybrid_bus:混合动力)
   */
  vehicleType?: string;

  /**
   * 座位数
   */
  seatCount?: string;

  /**
   * 车架号
   */
  vinNumber?: string;

  /**
   * 购买日期
   */
  purchaseDate?: string;

  /**
   * 上次维修时间
   */
  lastMaintenanceTime?: string;

  /**
   * 总里程(公里)
   */
  totalMileage?: string;

  /**
   * 主设备ID
   */
  mainDeviceId?: string | number;

  /**
   * 子设备ID
   */
  subDeviceIds?: string | number;

  /**
   * 状态(0:停用,1:正常,2:维修)
   */
  status?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 所属组织机构ID
   */
  deptId?: string | number;

  /**
   * 所属组织机构名称
   */
  deptName?: string;

}

export interface VehicleQuery extends PageQuery {

  /**
   * 车牌号
   */
  plateNumber?: string;

  /**
   * 车牌颜色(blue:蓝牌,yellow:黄牌,green:绿牌,white:白牌,black:黑牌)
   */
  plateColor?: string;

  /**
   * 车辆编号
   */
  vehicleNumber?: string;

  /**
   * 品牌型号
   */
  brandModel?: string;

  /**
   * 车辆类型(bus:公交车,electric_bus:电动公交,hybrid_bus:混合动力)
   */
  vehicleType?: string;

  /**
   * 座位数
   */
  seatCount?: string;

  /**
   * 车架号
   */
  vinNumber?: string;

  /**
   * 购买日期
   */
  purchaseDate?: string;

  /**
   * 上次维修时间
   */
  lastMaintenanceTime?: string;

  /**
   * 总里程(公里)
   */
  totalMileage?: string;

  /**
   * 主设备ID
   */
  mainDeviceId?: string | number;

  /**
   * 子设备ID
   */
  subDeviceIds?: string | number;

  /**
   * 状态(0:停用,1:正常,2:维修)
   */
  status?: string;

  /**
   * 所属组织机构ID
   */
  deptId?: string | number;

  /**
   * 所属组织机构名称
   */
  deptName?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}



