<template>
  <div class="welcome-container">
    <!-- 背景网格和渐变 -->
    <div class="background-layer">
      <div class="gradient-base"></div>
      <div class="gradient-overlay"></div>
      <div class="tech-grid"></div>
      <div class="data-streams">
        <div class="stream stream-1"></div>
        <div class="stream stream-2"></div>
        <div class="stream stream-3"></div>
      </div>
      <div class="floating-particles">
        <div class="particle particle-1"></div>
        <div class="particle particle-2"></div>
        <div class="particle particle-3"></div>
        <div class="particle particle-4"></div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 欢迎区域 -->
      <section class="welcome-section">
        <div class="welcome-content">
          <h1 class="welcome-title">欢迎回来，{{ username }}</h1>
          <p class="welcome-subtitle">{{ todayMessage }}</p>
        </div>
        <div class="hero-illustration">
          <div class="bus-3d-container">
            <div class="bus-model"></div>
            <div class="floating-elements">
              <div class="element element-1"></div>
              <div class="element element-2"></div>
              <div class="element element-3"></div>
            </div>
          </div>
        </div>
      </section>

      <!-- 功能卡片和指标区域 -->
      <section class="dashboard-layout">
        <!-- 主要功能区域 -->
        <div class="primary-features">
          <!-- 大屏监控 - 主要特色功能 -->
          <div class="hero-feature-card" @click="navigateTo(features[0])">
            <div class="hero-card-background"></div>
            <div class="hero-card-content">
              <div class="hero-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                  <rect x="2" y="6" width="20" height="12" rx="2" stroke="currentColor" stroke-width="1.5" />
                  <path d="M12 18v2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <path d="M8 22h8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <circle cx="6" cy="10" r="1" fill="currentColor" />
                  <circle cx="18" cy="14" r="1" fill="currentColor" />
                  <path d="M8 12l3 2 5-4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
              </div>
              <div class="hero-text">
                <h3>{{ features[0].title }}</h3>
                <p>{{ features[0].description }}</p>
              </div>
              <div class="hero-arrow">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M7 17l10-10M17 7H7v10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
          </div>

          <!-- 快速指标卡片 -->
          <div class="quick-metrics">
            <div v-for="(metric, index) in metrics" :key="metric.id"
                 class="quick-metric-card" :class="`metric-${metric.id}`">
              <div class="metric-header">
                <div class="metric-icon-small">
                  <svg v-if="metric.id === 'buses'" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="8" width="18" height="10" rx="2" stroke="currentColor" stroke-width="1.5" />
                    <circle cx="6" cy="12" r="1" fill="currentColor" />
                    <circle cx="18" cy="12" r="1" fill="currentColor" />
                  </svg>
                  <svg v-else-if="metric.id === 'trips'" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M8 2v3m8-3v3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                    <rect x="3" y="4" width="18" height="18" rx="2" stroke="currentColor" stroke-width="1.5" />
                    <path d="M3 9h18" stroke="currentColor" stroke-width="1.5" />
                  </svg>
                  <svg v-else-if="metric.id === 'passengers'" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="1.5" />
                    <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="1.5" />
                  </svg>
                  <svg v-else-if="metric.id === 'delays'" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M12 9v3l2 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5" />
                  </svg>
                  <svg v-else-if="metric.id === 'stations'" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="1.5"/>
                    <circle cx="12" cy="12" r="1" fill="currentColor"/>
                    <path d="M12 1v6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    <path d="M12 17v6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    <path d="M4.2 4.2l4.2 4.2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    <path d="M15.6 15.6l4.2 4.2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    <path d="M1 12h6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    <path d="M17 12h6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    <path d="M4.2 19.8l4.2-4.2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    <path d="M15.6 8.4l4.2-4.2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                  </svg>
                  <svg v-else-if="metric.id === 'routes'" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" stroke="currentColor" stroke-width="1.5"/>
                    <polyline points="3.29,7 12,12 20.71,7" stroke="currentColor" stroke-width="1.5"/>
                    <line x1="12" y1="22" x2="12" y2="12" stroke="currentColor" stroke-width="1.5"/>
                  </svg>
                </div>
                <div class="metric-trend-badge" :class="metric.trendClass">
                  {{ metric.trend }}
                </div>
              </div>
              <div class="metric-main">
                <div class="metric-value-large">{{ metric.value }}</div>
                <div class="metric-label-small">{{ metric.label }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 功能导航区域 -->
        <div class="navigation-area">
          <div class="nav-cards-flow">
            <div v-for="(feature, index) in features.slice(1, 8)" :key="feature.id"
                 class="nav-feature-card" :style="{ '--delay': index * 0.1 + 's' }"
                 @click="navigateTo(feature)">
              <div class="nav-card-glow"></div>
              <div class="nav-card-content">
                <div class="nav-feature-icon">
                  <svg v-if="feature.id === 'monitor'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="8" width="18" height="10" rx="2" stroke="currentColor" stroke-width="1.5" />
                    <circle cx="6" cy="12" r="1" fill="currentColor" />
                    <circle cx="18" cy="12" r="1" fill="currentColor" />
                  </svg>
                  <svg v-else-if="feature.id === 'schedule'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="4" width="18" height="18" rx="2" stroke="currentColor" stroke-width="1.5" />
                    <path d="M16 2v4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                    <path d="M8 2v4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                    <path d="M3 10h18" stroke="currentColor" stroke-width="1.5" />
                  </svg>
                  <svg v-else-if="feature.id === 'passenger'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="1.5" />
                    <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="1.5" />
                  </svg>
                  <svg v-else-if="feature.id === 'alerts'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="8" width="18" height="10" rx="2" stroke="currentColor" stroke-width="1.5" />
                    <circle cx="19" cy="5" r="3" fill="none" stroke="currentColor" stroke-width="1.5" />
                  </svg>
                  <svg v-else-if="feature.id === 'reports'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M3 3v18h18" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                    <path d="M18 17l-5-5-4 4-4-4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                  <svg v-else-if="feature.id === 'announcement'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M3 11v3a1 1 0 0 0 1 1h3l3 3v-10l-3 3H4a1 1 0 0 0-1 1z" stroke="currentColor" stroke-width="1.5" fill="currentColor" opacity="0.1" />
                    <path d="M13.5 5.5c1.5 1.5 1.5 3.5 0 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  </svg>
                  <svg v-else-if="feature.id === 'settings'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="1.5" />
                    <path d="M12 1v6m0 6v6" stroke="currentColor" stroke-width="1.5" opacity="0.5" />
                  </svg>
                </div>
                <div class="nav-feature-info">
                  <h4>{{ feature.title }}</h4>
                  <p>{{ feature.description }}</p>
                </div>
                <div class="nav-feature-arrow">→</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 实时数据区域 -->
        <div class="realtime-area">
          <!-- 今日概览 -->
          <div class="today-overview">
            <div class="overview-header">
              <h3>今日概览</h3>
              <div class="date-badge">{{ currentDate }}</div>
            </div>
            <div class="overview-grid">
              <div class="overview-item">
                <div class="overview-value">{{ currentTime }}</div>
                <div class="overview-label">当前时间</div>
              </div>
              <div class="overview-item">
                <div class="overview-value">25°C</div>
                <div class="overview-label">天气温度</div>
              </div>
            </div>
          </div>

          <!-- 车辆发车趋势图 -->
          <div class="dispatch-trend-chart">
            <div class="chart-header">
              <h3>车辆发车趋势</h3>
              <div class="chart-period">今日</div>
            </div>
            <div ref="dispatchChart" class="dispatch-chart"></div>
          </div>

          <!-- 快速操作 -->
          <div class="quick-actions">
            <div class="actions-header">
              <h3>快速操作</h3>
            </div>
            <div class="actions-grid">
              <button class="action-btn emergency" @click="navigateTo({path: '/monitor/dispatch/emergency'})">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" stroke="currentColor" stroke-width="2"/>
                  <path d="M12 9v4M12 17h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                紧急调度
              </button>
              <button class="action-btn maintenance" @click="navigateTo({path: '/basic/vehicle'})">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.77 3.77z" stroke="currentColor" stroke-width="1.5"/>
                </svg>
                车辆维护
              </button>
              <button class="action-btn report" @click="navigateTo({path: '/transit-monitor/display'})">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                  <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                  <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                </svg>
                生成报告
              </button>
              <button class="action-btn settings" @click="navigateTo({path: '/system/config'})">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                  <path d="M12 1v6m0 6v6" stroke="currentColor" stroke-width="2"/>
                </svg>
                系统配置
              </button>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup name="Index" lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import * as echarts from 'echarts';

const router = useRouter();

// 用户信息
const username = ref('管理员');
const userInitial = ref('管');

// 图表引用
const dispatchChart = ref(null);

// 时间显示
const currentTime = ref('');
const currentDate = ref('');
const todayMessage = ref('');

// 功能模块
const features = ref([
  {
    id: 'bigscreen',
    title: '大屏监控',
    description: '全屏可视化大屏监控展示',
    path: '/bigScreen'
  },
  {
    id: 'monitor',
    title: '实时车辆监控',
    description: '实时追踪车辆位置和运行状态',
    path: '/transit-monitor/vehicle'
  },
  {
    id: 'schedule',
    title: '线路调度管理',
    description: '智能排班和路线优化调度',
    path: '/schedule/template'
  },
  {
    id: 'passenger',
    title: '站点客流分析',
    description: '客流量统计和乘客行为分析',
    path: '/transit-monitor/display/passenger'
  },
  {
    id: 'alerts',
    title: '车辆状态告警',
    description: '异常情况监控和及时告警',
    path: '/transit-monitor/vehicle/alert'
  },
  {
    id: 'reports',
    title: '数据报表',
    description: '运营数据统计和分析报告',
    path: '/transit-monitor/display'
  },
  {
    id: 'announcement',
    title: '报站系统测试',
    description: '解析和测试F31格式报站配置文件',
    path: '/gateway/f31-parser'
  },
  {
    id: 'settings',
    title: '系统设置',
    description: '系统配置和参数管理',
    path: '/basic/operation'
  }
]);

// 关键指标
const metrics = ref([
  {
    id: 'buses',
    value: '156',
    label: '运营中车辆',
    trend: '+5.2%',
    trendClass: 'trend-up'
  },
  {
    id: 'trips',
    value: '2,847',
    label: '今日班次',
    trend: '+12.3%',
    trendClass: 'trend-up'
  },
  {
    id: 'passengers',
    value: '18,942',
    label: '当前载客量',
    trend: '+8.7%',
    trendClass: 'trend-up'
  },
  {
    id: 'delays',
    value: '3',
    label: '延误告警',
    trend: '-2.1%',
    trendClass: 'trend-down'
  },
  {
    id: 'stations',
    value: '126',
    label: '站点数量',
    trend: '总计',
    trendClass: 'trend-neutral'
  },
  {
    id: 'routes',
    value: '28',
    label: '路线数量',
    trend: '总计',
    trendClass: 'trend-neutral'
  }
]);

// 更新时间和消息
const updateDateTime = () => {
  const now = new Date();

  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  });

  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });

  const weekday = now.toLocaleDateString('zh-CN', { weekday: 'long' });
  todayMessage.value = `今天是${weekday}，公交线路运营状况良好`;
};

// 导航函数
const navigateTo = (feature: any) => {
  if (feature.id === 'bigscreen') {
    // 大屏页面在新窗口打开
    window.open(feature.path, '_blank');
  } else {
    // 其他页面在当前窗口打开
    router.push(feature.path);
  }
};

// 导航到主要功能模块（去掉原有的enterDashboard函数）

let timeInterval: NodeJS.Timeout;

// ECharts 实例
let chartInstance: echarts.ECharts | null = null;

// 初始化发车趋势图表
const initDispatchChart = () => {
  if (!dispatchChart.value) return;

  chartInstance = echarts.init(dispatchChart.value);

  const option = {
    backgroundColor: 'transparent',
    grid: {
      top: 20,
      left: 10,
      right: 10,
      bottom: 20,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00'],
      axisLine: {
        lineStyle: {
          color: 'rgba(148, 163, 184, 0.3)'
        }
      },
      axisLabel: {
        color: '#94a3b8',
        fontSize: 11
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#94a3b8',
        fontSize: 11
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(148, 163, 184, 0.1)',
          type: 'dashed'
        }
      }
    },
    series: [{
      name: '发车班次',
      type: 'line',
      data: [45, 67, 52, 73, 58, 84, 92, 48],
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [{
            offset: 0, color: '#409eff'
          }, {
            offset: 1, color: '#67c23a'
          }]
        }
      },
      itemStyle: {
        color: '#409eff',
        borderColor: '#409eff',
        borderWidth: 2
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(64, 158, 255, 0.3)'
          }, {
            offset: 1, color: 'rgba(64, 158, 255, 0.05)'
          }]
        }
      }
    }],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(15, 23, 42, 0.9)',
      borderColor: 'rgba(64, 158, 255, 0.3)',
      borderWidth: 1,
      textStyle: {
        color: '#f8fafc',
        fontSize: 12
      }
    }
  };

  chartInstance.setOption(option);

  // 响应式调整
  const handleResize = () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  };

  window.addEventListener('resize', handleResize);

  return () => {
    window.removeEventListener('resize', handleResize);
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
  };
};

onMounted(() => {
  updateDateTime();
  timeInterval = setInterval(updateDateTime, 1000);

  // 初始化图表
  nextTick(() => {
    initDispatchChart();
  });
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }

  // 销毁图表实例
  if (chartInstance) {
    chartInstance.dispose();
  }
});
</script>

<style scoped>
/* 基础容器 */
.welcome-container {
  position: relative;
  min-height: 100vh;
  height: 100%;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 40%, #334155 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
}

/* 确保背景覆盖整个视窗 */
.welcome-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 40%, #334155 100%);
  z-index: -2;
}

/* 背景层 - 优化多层背景动画 */
.background-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.gradient-base {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 40%, #334155 100%);
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(ellipse at top right, rgba(64, 158, 255, 0.15) 0%, transparent 60%),
    radial-gradient(ellipse at bottom left, rgba(103, 194, 58, 0.1) 0%, transparent 60%);
  opacity: 0.8;
  animation: gradientPulse 30s ease-in-out infinite;
  will-change: opacity, transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

@keyframes gradientPulse {
  0%,
  100% {
    opacity: 0;
    transform: scale(1) translateZ(0);
  }
  25% {
    opacity: 0.3;
    transform: scale(1.01) translateZ(0);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.02) translateZ(0);
  }
  75% {
    opacity: 0.3;
    transform: scale(1.01) translateZ(0);
  }
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(rgba(64, 158, 255, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(64, 158, 255, 0.1) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: gridMove 40s linear infinite;
  will-change: transform;
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(60px, 60px);
  }
}

/* 主内容区 - 调整布局，去掉顶部信息栏 */
.main-content {
  position: relative;
  min-height: calc(100vh - 80px);
  padding: 40px 28px 40px 28px;
  display: flex;
  flex-direction: column;
  z-index: 1;
}

/* 欢迎区域 - 调整位置 */
.welcome-section {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
  align-items: center;
  height: 200px;
  margin-bottom: 30px;
  flex-shrink: 0;
}

.welcome-content {
  padding: 10px 0;
}

.welcome-title {
  font-size: 42px;
  font-weight: 300;
  color: #f8fafc;
  margin: 0 0 16px 0;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.welcome-subtitle {
  font-size: 16px;
  color: #94a3b8;
  margin: 0;
  line-height: 1.6;
}

.hero-illustration {
  position: relative;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bus-3d-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.bus-model {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 160px;
  height: 64px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  border-radius: 32px 32px 16px 16px;
  box-shadow: 0 16px 32px rgba(64, 158, 255, 0.4);
  animation: busFloat 4s ease-in-out infinite;
}

.bus-model::before {
  content: '';
  position: absolute;
  top: 12px;
  left: 16px;
  right: 16px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
}

.bus-model::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 24px;
  right: 24px;
  height: 16px;
  background: rgba(64, 158, 255, 0.4);
  border-radius: 50%;
  filter: blur(8px);
}

@keyframes busFloat {
  0%,
  100% {
    transform: translate(-50%, -50%) translateY(0px);
  }
  50% {
    transform: translate(-50%, -50%) translateY(-8px);
  }
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.element {
  position: absolute;
  background: rgba(64, 158, 255, 0.2);
  border-radius: 50%;
  animation: elementFloat 6s ease-in-out infinite;
  border: 1px solid rgba(64, 158, 255, 0.3);
}

.element-1 {
  width: 40px;
  height: 40px;
  top: 15%;
  right: 10%;
  animation-delay: 0s;
}

.element-2 {
  width: 30px;
  height: 30px;
  bottom: 25%;
  left: 15%;
  animation-delay: 2s;
}

.element-3 {
  width: 24px;
  height: 24px;
  top: 55%;
  right: 25%;
  animation-delay: 4s;
}

@keyframes elementFloat {
  0%,
  100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-16px) scale(1.1);
  }
}

/* 内容网格区域 - 改为新的仪表板布局 */
.dashboard-layout {
  flex: 1;
  display: grid;
  grid-template-areas:
    "primary navigation realtime";
  grid-template-columns: 1.2fr 1fr 0.8fr;
  gap: 24px;
  min-height: 0;
  align-items: stretch;
}

/* 主要功能区域 */
.primary-features {
  grid-area: primary;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

/* 英雄功能卡片 - 大屏监控 */
.hero-feature-card {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(103, 194, 58, 0.1) 100%);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 32px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(64, 158, 255, 0.3);
  position: relative;
  overflow: hidden;
  min-height: 180px;
  display: flex;
  align-items: center;
}

.hero-card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(64, 158, 255, 0.1) 0%,
    rgba(103, 194, 58, 0.05) 50%,
    rgba(64, 158, 255, 0.08) 100%
  );
  opacity: 0;
  transition: opacity 0.4s ease;
}

.hero-feature-card:hover .hero-card-background {
  opacity: 1;
}

.hero-card-content {
  display: flex;
  align-items: center;
  gap: 24px;
  width: 100%;
  position: relative;
  z-index: 1;
}

.hero-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.2), rgba(103, 194, 58, 0.15));
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
  transition: all 0.4s ease;
  border: 1px solid rgba(64, 158, 255, 0.3);
  flex-shrink: 0;
}

.hero-feature-card:hover .hero-icon {
  transform: rotate(-5deg) scale(1.1);
  box-shadow: 0 12px 32px rgba(64, 158, 255, 0.4);
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.3), rgba(103, 194, 58, 0.2));
}

.hero-text {
  flex: 1;
}

.hero-text h3 {
  font-size: 24px;
  font-weight: 700;
  color: #f8fafc;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.hero-text p {
  font-size: 16px;
  color: #94a3b8;
  margin: 0;
  line-height: 1.5;
}

.hero-arrow {
  color: #64748b;
  transition: all 0.4s ease;
  opacity: 0.6;
}

.hero-feature-card:hover .hero-arrow {
  color: #409eff;
  transform: translate(8px, -8px) rotate(45deg);
  opacity: 1;
}

/* 快速指标 */
.quick-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  flex: 1;
}

.quick-metric-card {
  background: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 20px;
  flex: 1;
  border: 1px solid rgba(64, 158, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.quick-metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.quick-metric-card:hover::before {
  opacity: 1;
}

.quick-metric-card:hover {
  transform: translateY(-4px);
  border-color: rgba(64, 158, 255, 0.5);
  box-shadow: 0 12px 32px rgba(64, 158, 255, 0.2);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metric-icon-small {
  width: 40px;
  height: 40px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
  transition: all 0.3s ease;
}

.metric-buses .metric-icon-small {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

.metric-trips .metric-icon-small {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.metric-passengers .metric-icon-small {
  background: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
}

.metric-delays .metric-icon-small {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

.metric-stations .metric-icon-small {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

.metric-routes .metric-icon-small {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.metric-trend-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.metric-trend-badge.trend-up {
  background: rgba(103, 194, 58, 0.2);
  color: #67c23a;
  border: 1px solid rgba(103, 194, 58, 0.3);
}

.metric-trend-badge.trend-down {
  background: rgba(245, 108, 108, 0.2);
  color: #f56c6c;
  border: 1px solid rgba(245, 108, 108, 0.3);
}

.metric-trend-badge.trend-neutral {
  background: rgba(148, 163, 184, 0.2);
  color: #94a3b8;
  border: 1px solid rgba(148, 163, 184, 0.3);
}


.metric-main {
  text-align: left;
}

.metric-value-large {
  font-size: 32px;
  font-weight: 800;
  color: #f8fafc;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label-small {
  font-size: 14px;
  color: #94a3b8;
  font-weight: 500;
}

/* 导航区域 */
.navigation-area {
  grid-area: navigation;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.nav-cards-flow {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
  flex: 1;
}

.nav-feature-card {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(15px);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(64, 158, 255, 0.2);
  position: relative;
  overflow: hidden;
  animation: slideInRight 0.6s ease forwards;
  animation-delay: var(--delay);
  opacity: 0;
  transform: translateX(20px);
}

@keyframes slideInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.nav-card-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(64, 158, 255, 0.1) 50%,
    transparent 100%
  );
  transition: left 0.6s ease;
}

.nav-feature-card:hover .nav-card-glow {
  left: 100%;
}

.nav-card-content {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 1;
}

.nav-feature-icon {
  width: 48px;
  height: 48px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.nav-feature-card:hover .nav-feature-icon {
  transform: scale(1.1);
  background: rgba(64, 158, 255, 0.2);
}

.nav-feature-info {
  flex: 1;
}

.nav-feature-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #f8fafc;
  margin: 0 0 4px 0;
}

.nav-feature-info p {
  font-size: 13px;
  color: #94a3b8;
  margin: 0;
  line-height: 1.4;
}

.nav-feature-arrow {
  color: #64748b;
  transition: all 0.3s ease;
  font-size: 18px;
}

.nav-feature-card:hover .nav-feature-arrow {
  color: #409eff;
  transform: translateX(4px);
}

/* 实时数据区域 */
.realtime-area {
  grid-area: realtime;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

/* 系统状态总览 */
.system-overview {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.7));
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(64, 158, 255, 0.2);
  flex: 1;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.overview-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #f8fafc;
  margin: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #67c23a;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #67c23a;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

.overview-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-label {
  font-size: 13px;
  color: #94a3b8;
  min-width: 80px;
  font-weight: 500;
}

.stat-bar {
  flex: 1;
  height: 8px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.stat-fill {
  height: 100%;
  background: linear-gradient(90deg, #409eff, #67c23a);
  border-radius: 4px;
  transition: width 0.6s ease;
  position: relative;
}

.stat-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.stat-value {
  font-size: 13px;
  color: #f8fafc;
  font-weight: 600;
  min-width: 40px;
  text-align: right;
}

/* 今日概览 */
.today-overview {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.7));
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 20px;
  border: 1px solid rgba(64, 158, 255, 0.2);
  flex-shrink: 0;
}

.date-badge {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 16px;
}

.overview-item {
  text-align: center;
  padding: 12px;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(64, 158, 255, 0.1);
}

.overview-value {
  font-size: 20px;
  font-weight: 700;
  color: #f8fafc;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 12px;
  color: #94a3b8;
  font-weight: 500;
}

/* 车辆发车趋势图 */
.dispatch-trend-chart {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.7));
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 20px;
  border: 1px solid rgba(64, 158, 255, 0.2);
  flex: 1;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #f8fafc;
  margin: 0;
}

.chart-period {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.dispatch-chart {
  width: 100%;
  height: 200px;
}

/* 快速操作 */
.quick-actions {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.7));
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 20px;
  border: 1px solid rgba(64, 158, 255, 0.2);
  flex-shrink: 0;
}

.actions-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #f8fafc;
  margin: 0 0 16px 0;
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.action-btn {
  background: rgba(64, 158, 255, 0.1);
  border: 1px solid rgba(64, 158, 255, 0.2);
  color: #409eff;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.action-btn:hover {
  background: rgba(64, 158, 255, 0.2);
  border-color: rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.action-btn.emergency {
  background: rgba(245, 108, 108, 0.1);
  border-color: rgba(245, 108, 108, 0.2);
  color: #f56c6c;
}

.action-btn.emergency:hover {
  background: rgba(245, 108, 108, 0.2);
  border-color: rgba(245, 108, 108, 0.4);
}

.action-btn.maintenance {
  background: rgba(230, 162, 60, 0.1);
  border-color: rgba(230, 162, 60, 0.2);
  color: #e6a23c;
}

.action-btn.maintenance:hover {
  background: rgba(230, 162, 60, 0.2);
  border-color: rgba(230, 162, 60, 0.4);
}

.action-btn.report {
  background: rgba(103, 194, 58, 0.1);
  border-color: rgba(103, 194, 58, 0.2);
  color: #67c23a;
}

.action-btn.report:hover {
  background: rgba(103, 194, 58, 0.2);
  border-color: rgba(103, 194, 58, 0.4);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .welcome-section {
    grid-template-columns: 1fr;
    height: 140px;
    text-align: center;
  }

  .hero-illustration {
    display: none;
  }

  .dashboard-layout {
    grid-template-areas:
      "primary"
      "navigation"
      "realtime";
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }

  .quick-metrics {
    grid-template-columns: 1fr 1fr;
  }

  .nav-cards-flow {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 20px 20px;
  }

  .welcome-title {
    font-size: 28px;
  }

  .welcome-subtitle {
    font-size: 14px;
  }

  .dashboard-layout {
    gap: 16px;
  }

  .quick-metrics {
    grid-template-columns: 1fr 1fr;
  }

  .hero-feature-card {
    padding: 24px;
    min-height: 140px;
  }

  .hero-card-content {
    gap: 16px;
  }

  .hero-icon {
    width: 60px;
    height: 60px;
  }

  .hero-text h3 {
    font-size: 20px;
  }

  .hero-text p {
    font-size: 14px;
  }

  .nav-cards-flow {
    grid-template-columns: 1fr;
  }

  .nav-feature-card {
    padding: 16px;
  }

  .secondary-metric-card {
    padding: 20px;
  }

  .secondary-metric-value {
    font-size: 32px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 10px 15px;
  }

  .welcome-section {
    height: 120px;
  }

  .welcome-title {
    font-size: 24px;
  }

  .dashboard-layout {
    gap: 12px;
  }

  .hero-feature-card {
    padding: 20px;
    min-height: 120px;
  }

  .hero-card-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .hero-icon {
    width: 50px;
    height: 50px;
  }

  .nav-feature-card {
    padding: 12px;
  }

  .nav-card-content {
    gap: 12px;
  }

  .nav-feature-icon {
    width: 40px;
    height: 40px;
  }

  .secondary-metric-card {
    padding: 16px;
  }

  .secondary-metric-icon {
    width: 48px;
    height: 48px;
  }

  .secondary-metric-value {
    font-size: 28px;
  }
}

/* 科技感装饰元素 */
.data-streams {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.stream {
  position: absolute;
  width: 2px;
  height: 100px;
  background: linear-gradient(180deg, transparent 0%, rgba(64, 158, 255, 0.6) 50%, transparent 100%);
  animation: streamFlow 8s linear infinite;
}

.stream-1 {
  left: 20%;
  animation-delay: 0s;
}

.stream-2 {
  left: 60%;
  animation-delay: 3s;
}

.stream-3 {
  right: 25%;
  animation-delay: 6s;
}

@keyframes streamFlow {
  0% {
    transform: translateY(-100px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(calc(100vh + 100px));
    opacity: 0;
  }
}

.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(64, 158, 255, 0.4);
  border-radius: 50%;
  animation: particleFloat 12s ease-in-out infinite;
}

.particle-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.particle-2 {
  top: 40%;
  right: 15%;
  animation-delay: 3s;
}

.particle-3 {
  bottom: 30%;
  left: 25%;
  animation-delay: 6s;
}

.particle-4 {
  top: 60%;
  right: 30%;
  animation-delay: 9s;
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-20px) scale(1.2);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) scale(0.8);
    opacity: 0.6;
  }
  75% {
    transform: translateY(-30px) scale(1.1);
    opacity: 0.7;
  }
}
</style>
