import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MaintainLogVO, MaintainLogForm, MaintainLogQuery } from './types';

/**
 * 查询维修保养记录列表
 * @param query
 * @returns {*}
 */

export const listMaintainLog = (query?: MaintainLogQuery): AxiosPromise<MaintainLogVO[]> => {
  return request({
    url: '/biz/maintainLog/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询维修保养记录详细
 * @param id
 */
export const getMaintainLog = (id: string | number): AxiosPromise<MaintainLogVO> => {
  return request({
    url: '/biz/maintainLog/' + id,
    method: 'get'
  });
};

/**
 * 新增维修保养记录
 * @param data
 */
export const addMaintainLog = (data: MaintainLogForm) => {
  return request({
    url: '/biz/maintainLog',
    method: 'post',
    data: data
  });
};

/**
 * 修改维修保养记录
 * @param data
 */
export const updateMaintainLog = (data: MaintainLogForm) => {
  return request({
    url: '/biz/maintainLog',
    method: 'put',
    data: data
  });
};

/**
 * 删除维修保养记录
 * @param id
 */
export const delMaintainLog = (id: string | number | Array<string | number>) => {
  return request({
    url: '/biz/maintainLog/' + id,
    method: 'delete'
  });
};
